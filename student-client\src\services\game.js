import { storageService } from './storage.js'
import { apiService } from './api.js'

class GameService {
    constructor() {
        this.achievements = []
        this.gameData = {}
        this.soundEnabled = true
        this.animationsEnabled = true
    }
    
    init() {
        this.loadGameData()
        this.loadSettings()
        console.log('🎮 Game Service initialized')
    }
    
    loadGameData() {
        this.gameData = storageService.getGameData()
        this.achievements = storageService.getAchievements()
    }
    
    loadSettings() {
        const settings = storageService.getSettings()
        this.soundEnabled = settings.soundEnabled
        this.animationsEnabled = settings.animationsEnabled
    }
    
    // Points and Experience
    async addPoints(points, reason = '') {
        const oldPoints = this.gameData.points || 0
        const newPoints = oldPoints + points
        
        this.gameData.points = newPoints
        this.gameData.experience = (this.gameData.experience || 0) + points
        
        // Check for level up
        const oldLevel = this.gameData.level || 1
        const newLevel = this.calculateLevel(this.gameData.experience)
        
        if (newLevel > oldLevel) {
            this.gameData.level = newLevel
            this.triggerLevelUp(newLevel)
        }
        
        // Save locally
        storageService.setGameData(this.gameData)
        
        // Sync with server
        try {
            await apiService.updatePoints(newPoints)
        } catch (error) {
            console.error('🚨 Failed to sync points with server:', error)
        }
        
        // Trigger achievement checks
        this.checkAchievements('points_earned', { points, totalPoints: newPoints, reason })
        
        console.log(`🌟 Added ${points} points! Total: ${newPoints}`)
        return newPoints
    }
    
    calculateLevel(experience) {
        // Level calculation: each level requires more experience
        // Level 1: 0-99, Level 2: 100-299, Level 3: 300-599, etc.
        return Math.floor(Math.sqrt(experience / 100)) + 1
    }
    
    getExperienceForNextLevel(currentLevel) {
        return Math.pow(currentLevel, 2) * 100
    }
    
    getProgressToNextLevel() {
        const currentLevel = this.gameData.level || 1
        const currentExp = this.gameData.experience || 0
        const expForCurrentLevel = this.getExperienceForNextLevel(currentLevel - 1)
        const expForNextLevel = this.getExperienceForNextLevel(currentLevel)
        
        const progressExp = currentExp - expForCurrentLevel
        const requiredExp = expForNextLevel - expForCurrentLevel
        
        return {
            current: progressExp,
            required: requiredExp,
            percentage: Math.round((progressExp / requiredExp) * 100)
        }
    }
    
    // Achievements
    async unlockAchievement(achievementId, achievementData = {}) {
        const achievement = {
            id: achievementId,
            ...achievementData,
            unlockedAt: new Date().toISOString()
        }
        
        const wasAdded = storageService.addAchievement(achievement)
        
        if (wasAdded) {
            // Sync with server
            try {
                await apiService.unlockAchievement(achievementId)
            } catch (error) {
                console.error('🚨 Failed to sync achievement with server:', error)
            }
            
            // Trigger celebration
            this.triggerAchievementUnlock(achievement)
            
            // Award points for achievement
            const points = achievementData.points || 50
            await this.addPoints(points, `Achievement: ${achievementData.name}`)
            
            return true
        }
        
        return false
    }
    
    checkAchievements(action, data = {}) {
        // Define achievement conditions
        const achievementChecks = {
            'points_earned': this.checkPointsAchievements.bind(this),
            'book_completed': this.checkReadingAchievements.bind(this),
            'login': this.checkStreakAchievements.bind(this),
            'task_completed': this.checkTaskAchievements.bind(this)
        }
        
        const checker = achievementChecks[action]
        if (checker) {
            checker(data)
        }
    }
    
    checkPointsAchievements(data) {
        const totalPoints = data.totalPoints || 0
        const unlockedAchievements = storageService.getAchievements()
        
        const pointsMilestones = [
            { id: 'first_points', threshold: 10, name: 'First Steps', description: 'Earned your first 10 points!', points: 25 },
            { id: 'points_100', threshold: 100, name: 'Century Club', description: 'Earned 100 points!', points: 50 },
            { id: 'points_500', threshold: 500, name: 'Point Master', description: 'Earned 500 points!', points: 100 },
            { id: 'points_1000', threshold: 1000, name: 'Point Legend', description: 'Earned 1000 points!', points: 200 }
        ]
        
        pointsMilestones.forEach(milestone => {
            if (totalPoints >= milestone.threshold && 
                !unlockedAchievements.find(a => a.id === milestone.id)) {
                this.unlockAchievement(milestone.id, milestone)
            }
        })
    }
    
    checkReadingAchievements(data) {
        const booksRead = (this.gameData.booksRead || 0) + 1
        this.gameData.booksRead = booksRead
        storageService.setGameData(this.gameData)
        
        const unlockedAchievements = storageService.getAchievements()
        
        const readingMilestones = [
            { id: 'first_book', threshold: 1, name: 'Bookworm', description: 'Completed your first book!', points: 100 },
            { id: 'books_5', threshold: 5, name: 'Reader', description: 'Completed 5 books!', points: 150 },
            { id: 'books_10', threshold: 10, name: 'Book Master', description: 'Completed 10 books!', points: 250 },
            { id: 'books_25', threshold: 25, name: 'Library Champion', description: 'Completed 25 books!', points: 500 }
        ]
        
        readingMilestones.forEach(milestone => {
            if (booksRead >= milestone.threshold && 
                !unlockedAchievements.find(a => a.id === milestone.id)) {
                this.unlockAchievement(milestone.id, milestone)
            }
        })
    }
    
    checkStreakAchievements(data) {
        const today = new Date().toDateString()
        const lastLogin = this.gameData.lastLoginDate
        
        if (lastLogin !== today) {
            const yesterday = new Date()
            yesterday.setDate(yesterday.getDate() - 1)
            
            if (lastLogin === yesterday.toDateString()) {
                // Continuing streak
                this.gameData.streak = (this.gameData.streak || 0) + 1
            } else {
                // New streak
                this.gameData.streak = 1
            }
            
            this.gameData.lastLoginDate = today
            storageService.setGameData(this.gameData)
            
            // Check streak achievements
            const streak = this.gameData.streak
            const unlockedAchievements = storageService.getAchievements()
            
            const streakMilestones = [
                { id: 'streak_3', threshold: 3, name: 'Consistent Reader', description: '3-day reading streak!', points: 75 },
                { id: 'streak_7', threshold: 7, name: 'Weekly Warrior', description: '7-day reading streak!', points: 150 },
                { id: 'streak_30', threshold: 30, name: 'Monthly Master', description: '30-day reading streak!', points: 500 }
            ]
            
            streakMilestones.forEach(milestone => {
                if (streak >= milestone.threshold && 
                    !unlockedAchievements.find(a => a.id === milestone.id)) {
                    this.unlockAchievement(milestone.id, milestone)
                }
            })
        }
    }
    
    checkTaskAchievements(data) {
        // Implementation for task-related achievements
        const tasksCompleted = (this.gameData.tasksCompleted || 0) + 1
        this.gameData.tasksCompleted = tasksCompleted
        storageService.setGameData(this.gameData)
    }
    
    // Game Events
    triggerLevelUp(newLevel) {
        console.log(`🎊 Level Up! Now level ${newLevel}`)
        
        if (this.soundEnabled) {
            this.playSound('levelup')
        }
        
        // Trigger visual celebration
        this.triggerCelebration('levelup')
        
        // Award level up bonus
        const bonusPoints = newLevel * 10
        this.addPoints(bonusPoints, `Level ${newLevel} Bonus`)
    }
    
    triggerAchievementUnlock(achievement) {
        console.log(`🏆 Achievement Unlocked: ${achievement.name}`)
        
        if (this.soundEnabled) {
            this.playSound('achievement')
        }
        
        // Trigger visual celebration
        this.triggerCelebration('achievement', achievement)
    }
    
    triggerCelebration(type, data = {}) {
        // Dispatch custom event for UI to handle
        window.dispatchEvent(new CustomEvent('game-celebration', {
            detail: { type, data }
        }))
    }
    
    playSound(soundType) {
        // Simple sound implementation
        // In a real app, you'd load and play actual sound files
        if (!this.soundEnabled) return
        
        const sounds = {
            'points': { frequency: 800, duration: 200 },
            'achievement': { frequency: 1000, duration: 500 },
            'levelup': { frequency: 1200, duration: 800 },
            'error': { frequency: 300, duration: 300 }
        }
        
        const sound = sounds[soundType]
        if (sound && window.AudioContext) {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)()
                const oscillator = audioContext.createOscillator()
                const gainNode = audioContext.createGain()
                
                oscillator.connect(gainNode)
                gainNode.connect(audioContext.destination)
                
                oscillator.frequency.value = sound.frequency
                oscillator.type = 'sine'
                
                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime)
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + sound.duration / 1000)
                
                oscillator.start(audioContext.currentTime)
                oscillator.stop(audioContext.currentTime + sound.duration / 1000)
            } catch (error) {
                console.log('🔇 Sound not available')
            }
        }
    }
    
    // Data Access
    async getUserGameData() {
        try {
            // Try to get fresh data from server
            const serverData = await apiService.getGameData()
            
            // Merge with local data
            this.gameData = { ...this.gameData, ...serverData }
            storageService.setGameData(this.gameData)
            
            return this.gameData
        } catch (error) {
            console.error('🚨 Failed to fetch game data from server:', error)
            // Return local data as fallback
            return this.gameData
        }
    }
    
    updatePoints(points) {
        this.gameData.points = points
        storageService.setGameData(this.gameData)
    }
    
    updateLevel(level) {
        this.gameData.level = level
        storageService.setGameData(this.gameData)
    }
    
    getGameData() {
        return this.gameData
    }
    
    getAchievements() {
        return storageService.getAchievements()
    }
    
    // Settings
    setSoundEnabled(enabled) {
        this.soundEnabled = enabled
        const settings = storageService.getSettings()
        settings.soundEnabled = enabled
        storageService.setSettings(settings)
    }
    
    setAnimationsEnabled(enabled) {
        this.animationsEnabled = enabled
        const settings = storageService.getSettings()
        settings.animationsEnabled = enabled
        storageService.setSettings(settings)
    }
}

export const gameService = new GameService()
