{"name": "student-client", "version": "1.0.0", "description": "Gamified mobile reading tracker for students", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "vite preview --port 3001", "setup": "node scripts/dev-setup.js"}, "keywords": ["education", "reading", "student", "gamification", "mobile", "spa"], "author": "", "license": "MIT", "devDependencies": {"vite": "^5.0.0", "@vitejs/plugin-legacy": "^5.0.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6"}, "dependencies": {"alpinejs": "^3.13.3", "daisyui": "^4.4.19", "axios": "^1.6.2", "@heroicons/react": "^2.0.18"}}