<?php

namespace App\MoonShine\Resources;

use App\Models\StoryChapter;
use App\Models\Story;
use App\Models\StoryRule;
use App\Models\Role;
use MoonShine\Support\Attributes\Icon;
use MoonShine\UI\Fields\{Text, Textarea, Number};
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Components\Tabs;
use MoonShine\UI\Components\Tabs\Tab;

#[Icon('document-text')]
class StoryChapterResource extends BaseResource
{
    protected string $model = StoryChapter::class;

    protected string $column = 'full_title';

    protected array $with = ['story', 'unlockRule', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.story_chapters');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            BelongsTo::make(__('admin.story'), 'story', 
                formatted: fn(Story $story) => $story->title,
                resource: StoryResource::class)
                ->sortable(),
            
            Number::make(__('admin.sequence'), 'sequence')
                ->sortable(),
            
            Text::make(__('admin.title'), 'title')
                ->sortable(),
            
            Text::make(__('admin.full_title'), 'full_title'),
            
            BelongsTo::make(__('admin.unlock_rule'), 'unlockRule', 
                formatted: fn(?StoryRule $rule) => $rule?->description ?? 'No rule',
                resource: StoryRuleResource::class)
                ->nullable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Tabs::make([
                    Tab::make(__('admin.main_information'), [
                        BelongsTo::make(__('admin.story'), 'story', 
                            formatted: fn(Story $story) => $story->title,
                            resource: StoryResource::class)
                            ->required()
                            ->placeholder(__('admin.select_story')),
                        
                        Flex::make([
                            Number::make(__('admin.sequence'), 'sequence')
                                ->required()
                                ->min(1)
                                ->placeholder(__('admin.enter_sequence')),
                            
                            Text::make(__('admin.title'), 'title')
                                ->required()
                                ->placeholder(__('admin.enter_title')),
                        ]),
                        
                        Textarea::make(__('admin.description'), 'description')
                            ->required()
                            ->placeholder(__('admin.enter_description')),
                        
                        BelongsTo::make(__('admin.unlock_rule'), 'unlockRule', 
                            formatted: fn(?StoryRule $rule) => $rule?->description ?? 'No rule',
                            resource: StoryRuleResource::class)
                            ->nullable()
                            ->placeholder(__('admin.select_unlock_rule')),
                    ]),
                    
                    Tab::make(__('admin.map_coordinates'), [
                        Flex::make([
                            Number::make(__('admin.map_start_x'), 'map_start_x')
                                ->nullable()
                                ->min(0),
                            
                            Number::make(__('admin.map_start_y'), 'map_start_y')
                                ->nullable()
                                ->min(0),
                        ]),
                        
                        Flex::make([
                            Number::make(__('admin.map_end_x'), 'map_end_x')
                                ->nullable()
                                ->min(0),
                            
                            Number::make(__('admin.map_end_y'), 'map_end_y')
                                ->nullable()
                                ->min(0),
                        ]),
                    ]),
                ]),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(__('admin.story'), 'story', 
                formatted: fn(Story $story) => $story->title,
                resource: StoryResource::class),
            Number::make(__('admin.sequence'), 'sequence'),
            Text::make(__('admin.title'), 'title'),
            Text::make(__('admin.full_title'), 'full_title'),
            Textarea::make(__('admin.description'), 'description'),
            BelongsTo::make(__('admin.unlock_rule'), 'unlockRule', 
                formatted: fn(?StoryRule $rule) => $rule?->description ?? 'No rule',
                resource: StoryRuleResource::class),
            Number::make(__('admin.map_start_x'), 'map_start_x'),
            Number::make(__('admin.map_start_y'), 'map_start_y'),
            Number::make(__('admin.map_end_x'), 'map_end_x'),
            Number::make(__('admin.map_end_y'), 'map_end_y'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'story_id' => ['required', 'exists:stories,id'],
            'title' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string'],
            'sequence' => ['required', 'integer', 'min:1'],
            'unlock_rule_id' => ['nullable', 'exists:story_rules,id'],
            'map_start_x' => ['nullable', 'integer', 'min:0'],
            'map_start_y' => ['nullable', 'integer', 'min:0'],
            'map_end_x' => ['nullable', 'integer', 'min:0'],
            'map_end_y' => ['nullable', 'integer', 'min:0'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function checkCreatePermission($user): bool
    {
        // System admins, group school admins, and school admins can create chapters
        return $user->isSystemAdmin() || 
               $user->hasRoleLevel(Role::LEVEL_GROUP_SCHOOL_ADMIN) ||
               $user->hasRoleLevel(Role::LEVEL_SCHOOL_ADMIN);
    }

    protected function checkUpdatePermission($user): bool
    {
        // System admins, group school admins, and school admins can update chapters
        return $user->isSystemAdmin() || 
               $user->hasRoleLevel(Role::LEVEL_GROUP_SCHOOL_ADMIN) ||
               $user->hasRoleLevel(Role::LEVEL_SCHOOL_ADMIN);
    }

    protected function checkDeletePermission($user): bool
    {
        // Only system admins and group school admins can delete chapters
        return $user->isSystemAdmin() || 
               $user->hasRoleLevel(Role::LEVEL_GROUP_SCHOOL_ADMIN);
    }

    protected function getSearchFields(): array
    {
        return ['title', 'description'];
    }

    protected function getDefaultSort(): array
    {
        return ['sequence' => 'asc'];
    }
}
