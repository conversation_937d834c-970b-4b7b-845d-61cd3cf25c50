<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Program;
use App\Models\ProgramTeam;
use App\MoonShine\Resources\BaseResource;
use App\MoonShine\Resources\ProgramResource;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;

/**
 * @extends BaseResource<ProgramTeam>
 */
class ProgramTeamResource extends BaseResource
{
    protected string $model = ProgramTeam::class;

    protected string $column = 'name';

    protected array $with = ['program', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.program_teams');
    }



    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name)
                ->sortable(),
            Text::make(__('admin.team_name'), 'name')
                ->sortable(),
            Text::make(__('admin.member_count'), 'member_count')
                ->badge('blue'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(__('admin.programs'), 'program', 
                    formatted: fn(Program $program) => $program->name,
                    resource: ProgramResource::class)
                    ->required()
                    ->placeholder(__('admin.select_program')),
                
                Text::make(__('admin.team_name'), 'name')
                    ->required()
                    ->placeholder(__('admin.enter_team_name')),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            ...parent::getCommonDetailFields(),
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name),
            Text::make(__('admin.team_name'), 'name'),
            Text::make(__('admin.member_count'), 'member_count')
                ->badge('blue'),
        ];
    }

    protected function filters(): iterable
    {
        return [
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name,
                resource: ProgramResource::class),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'program_id' => ['required', 'exists:programs,id'],
            'name' => ['required', 'string', 'max:255'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return ['name'];
    }

    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }

    protected function checkCreatePermission($user): bool
    {
        // System admins, school admins, and teachers can create teams
        return $user->isSystemAdmin() ||
               $user->hasRoleLevel(\App\Models\Role::LEVEL_SCHOOL_ADMIN) ||
               $user->hasRoleLevel(\App\Models\Role::LEVEL_TEACHER);
    }

    protected function checkUpdatePermission($user): bool
    {
        // System admins, school admins, and teachers can update teams
        return $user->isSystemAdmin() ||
               $user->hasRoleLevel(\App\Models\Role::LEVEL_SCHOOL_ADMIN) ||
               $user->hasRoleLevel(\App\Models\Role::LEVEL_TEACHER);
    }

    protected function checkDeletePermission($user): bool
    {
        // System admins and school admins can delete teams
        return $user->isSystemAdmin() ||
               $user->hasRoleLevel(\App\Models\Role::LEVEL_SCHOOL_ADMIN);
    }
}
