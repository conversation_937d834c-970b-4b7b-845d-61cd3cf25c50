# Teacher Reading Tracker - Mobile Client

A mobile-first web application for teachers to manage gamified reading programs. Built with Tailwind CSS, DaisyUI, Alpine.js, and Vite.

## Features

- **Mobile-First Design**: Optimized for tablets and phones with touch-friendly interfaces
- **Authentication**: JWT-based authentication with session persistence
- **Dashboard**: Overview of assigned programs, students, and reading progress
- **Program Management**: Detailed views of reading programs with teams and books
- **Pull-to-Refresh**: Mobile-native refresh functionality
- **Offline-Ready**: Local storage for authentication and basic caching
- **Progressive Web App**: Can be installed on mobile devices

## Tech Stack

- **Frontend Framework**: Alpine.js 3.x
- **Build Tool**: Vite 5.x
- **Styling**: Tailwind CSS 3.x + DaisyUI 4.x
- **HTTP Client**: Axios
- **Storage**: LocalStorage with service layer
- **PWA**: Web App Manifest + Service Worker ready

## Project Structure

```
teacher-client/
├── public/                 # Static assets
│   ├── manifest.json      # PWA manifest
│   └── favicon.svg        # App icon
├── src/
│   ├── components/        # Alpine.js components
│   │   ├── auth/         # Authentication components
│   │   ├── dashboard/    # Dashboard components
│   │   ├── layout/       # Layout components
│   │   └── program/      # Program detail components
│   ├── services/         # Business logic services
│   │   ├── api.js        # API communication
│   │   ├── auth.js       # Authentication service
│   │   ├── router.js     # Client-side routing
│   │   └── storage.js    # Local storage management
│   ├── styles/           # CSS and styling
│   │   └── main.css      # Main stylesheet
│   └── main.js           # Application entry point
├── index.html            # Main HTML template
├── package.json          # Dependencies and scripts
├── tailwind.config.js    # Tailwind configuration
├── vite.config.js        # Vite build configuration
└── README.md            # This file
```

## Setup Instructions

### Prerequisites

- Node.js 18+ and npm
- Laravel backend running on `http://localhost:8000`

### Installation

1. **Clone or navigate to the teacher-client directory**
   ```bash
   cd teacher-client
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` to match your Laravel backend URL:
   ```
   VITE_API_BASE_URL=http://localhost:8000/api
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```
   
   The app will be available at `http://localhost:3000`

### Build for Production

```bash
npm run build
```

The built files will be in the `dist/` directory and can be served by any static file server.

### Preview Production Build

```bash
npm run preview
```

## API Integration

The application expects the following API endpoints from the Laravel backend:

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout  
- `GET /api/auth/user` - Get current user
- `POST /api/auth/refresh` - Refresh JWT token

### Teacher Data
- `GET /api/teacher/programs` - Get teacher's assigned programs
- `GET /api/teacher/programs/{id}` - Get program details
- `GET /api/teacher/programs/{id}/teams` - Get program teams
- `GET /api/teacher/programs/{id}/books` - Get program books
- `GET /api/teacher/active-term` - Get active term information

### Expected Data Formats

#### Login Request
```json
{
  "email": "<EMAIL>",
  "password": "password"
}
```

#### Login Response
```json
{
  "token": "jwt_token_here",
  "user": {
    "id": 1,
    "name": "John Doe",
    "title": "Mr.",
    "email": "<EMAIL>",
    "role": {
      "id": 3,
      "name": "Teacher",
      "level": 3
    }
  }
}
```

#### Programs Response
```json
{
  "programs": [
    {
      "id": 1,
      "name": "Summer Reading Program",
      "description": "A fun summer reading challenge",
      "active": true,
      "start_date": "2024-06-01",
      "end_date": "2024-08-31",
      "student_count": 25,
      "team_count": 5,
      "book_count": 10,
      "completed_books": 3,
      "total_books": 10
    }
  ]
}
```

## Mobile Features

### Touch-Friendly Design
- Minimum 44px touch targets
- Optimized button sizes and spacing
- Swipe gestures for navigation

### Pull-to-Refresh
- Native mobile pull-to-refresh functionality
- Visual feedback during refresh
- Automatic data updates

### Responsive Layout
- Mobile-first responsive design
- Optimized for portrait orientation
- Safe area handling for notched devices

### Progressive Web App
- Can be installed on home screen
- Offline-ready architecture
- App-like experience

## Development

### Adding New Components

1. Create component file in appropriate directory under `src/components/`
2. Export Alpine.js data function
3. Import in `src/main.js`
4. Use in HTML templates

Example component:
```javascript
// src/components/example/MyComponent.js
import Alpine from 'alpinejs'

Alpine.data('myComponent', () => ({
    data: 'Hello World',
    
    init() {
        console.log('Component initialized')
    },
    
    handleClick() {
        this.data = 'Clicked!'
    }
}))
```

### Adding New Routes

1. Define route in `src/services/router.js`
2. Add route handling in main template
3. Create corresponding component

### Styling Guidelines

- Use Tailwind utility classes
- Leverage DaisyUI components
- Follow mobile-first approach
- Maintain consistent spacing and colors

## Browser Support

- Chrome/Safari on iOS 12+
- Chrome on Android 8+
- Modern desktop browsers (Chrome, Firefox, Safari, Edge)

## Contributing

1. Follow the existing code structure
2. Use TypeScript-style JSDoc comments
3. Test on mobile devices
4. Ensure accessibility compliance
5. Follow mobile design patterns

## License

This project is part of the gamified reading tracker system.
