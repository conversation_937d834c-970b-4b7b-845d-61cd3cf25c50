<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Program;
use App\Models\ProgramUserLevel;
use App\Models\StoryChapter;
use App\Models\TermUser;
use App\MoonShine\Resources\BaseResource;
use App\MoonShine\Resources\ProgramResource;
use App\MoonShine\Resources\TermUserResource;
use App\MoonShine\Resources\StoryChapterResource;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Components\Layout\Box;

/**
 * @extends BaseResource<ProgramUserLevel>
 */
class ProgramUserLevelResource extends BaseResource
{
    protected string $model = ProgramUserLevel::class;

    protected array $with = ['program', 'termUser', 'storyChapter', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.program_user_levels');
    }



    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name)
                ->sortable(),
            BelongsTo::make(__('admin.term_users'), 'termUser', 
                formatted: fn(TermUser $termUser) => $termUser->user->name)
                ->sortable(),
            BelongsTo::make(__('admin.current_chapter'), 'storyChapter', 
                formatted: fn(StoryChapter $chapter) => $chapter->title)
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(__('admin.programs'), 'program', 
                    formatted: fn(Program $program) => $program->name,
                    resource: ProgramResource::class)
                    ->required()
                    ->placeholder(__('admin.select_program')),
                
                BelongsTo::make(__('admin.term_users'), 'termUser', 
                    formatted: fn(TermUser $termUser) => $termUser->user->name,
                    resource: TermUserResource::class)
                    ->required()
                    ->placeholder(__('admin.select_student')),
                
                BelongsTo::make(__('admin.current_chapter'), 'storyChapter', 
                    formatted: fn(StoryChapter $chapter) => $chapter->title,
                    resource: StoryChapterResource::class)
                    ->required()
                    ->placeholder(__('admin.select_chapter')),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            ...parent::getCommonDetailFields(),
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name),
            BelongsTo::make(__('admin.term_users'), 'termUser', 
                formatted: fn(TermUser $termUser) => $termUser->user->name),
            BelongsTo::make(__('admin.current_chapter'), 'storyChapter', 
                formatted: fn(StoryChapter $chapter) => $chapter->title),
        ];
    }

    protected function filters(): iterable
    {
        return [
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name,
                resource: ProgramResource::class),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'program_id' => ['required', 'exists:programs,id'],
            'term_user_id' => ['required', 'exists:term_users,id'],
            'story_chapter_id' => ['required', 'exists:story_chapters,id'],
            ...parent::getCommonRules($item),
        ];
    }

    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }

    protected function checkCreatePermission($user): bool
    {
        // System admins, school admins, and teachers can track user levels
        return $user->isSystemAdmin() ||
               $user->hasRoleLevel(\App\Models\Role::LEVEL_SCHOOL_ADMIN) ||
               $user->hasRoleLevel(\App\Models\Role::LEVEL_TEACHER);
    }

    protected function checkUpdatePermission($user): bool
    {
        // System admins, school admins, and teachers can update user levels
        return $user->isSystemAdmin() ||
               $user->hasRoleLevel(\App\Models\Role::LEVEL_SCHOOL_ADMIN) ||
               $user->hasRoleLevel(\App\Models\Role::LEVEL_TEACHER);
    }

    protected function checkDeletePermission($user): bool
    {
        // System admins and school admins can delete user levels
        return $user->isSystemAdmin() ||
               $user->hasRoleLevel(\App\Models\Role::LEVEL_SCHOOL_ADMIN);
    }
}
