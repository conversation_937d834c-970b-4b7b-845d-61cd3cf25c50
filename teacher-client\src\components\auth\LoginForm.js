import Alpine from 'alpinejs'

Alpine.data('loginForm', () => ({
    // Form data
    email: '',
    password: '',
    rememberMe: false,
    
    // Form state
    isLoading: false,
    errors: {},
    showPassword: false,
    
    // Initialize component
    init() {
        // Focus on email input when component loads
        this.$nextTick(() => {
            this.$refs.emailInput?.focus()
        })
        
        // Check for saved email
        const savedEmail = localStorage.getItem('teacher_app_saved_email')
        if (savedEmail) {
            this.email = savedEmail
            this.rememberMe = true
        }
    },
    
    // Form validation
    validateForm() {
        this.errors = {}
        
        if (!this.email) {
            this.errors.email = 'Email is required'
        } else if (!this.isValidEmail(this.email)) {
            this.errors.email = 'Please enter a valid email address'
        }
        
        if (!this.password) {
            this.errors.password = 'Password is required'
        } else if (this.password.length < 6) {
            this.errors.password = 'Password must be at least 6 characters'
        }
        
        return Object.keys(this.errors).length === 0
    },
    
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        return emailRegex.test(email)
    },
    
    // Handle form submission
    async handleSubmit() {
        if (!this.validateForm()) {
            return
        }
        
        this.isLoading = true
        this.errors = {}
        
        try {
            const result = await this.$store.app.login({
                email: this.email.trim(),
                password: this.password
            })
            
            if (result.success) {
                // Save email if remember me is checked
                if (this.rememberMe) {
                    localStorage.setItem('teacher_app_saved_email', this.email)
                } else {
                    localStorage.removeItem('teacher_app_saved_email')
                }
                
                // Show success message
                this.$store.app.showToast('Login successful!', 'success')
                
                // Clear form
                this.clearForm()
            } else {
                // Handle login error
                if (result.errors) {
                    this.errors = result.errors
                } else {
                    this.errors.general = result.message || 'Login failed'
                }
                
                // Show error toast
                this.$store.app.showToast(result.message || 'Login failed', 'error')
            }
        } catch (error) {
            console.error('Login form error:', error)
            this.errors.general = 'An unexpected error occurred'
            this.$store.app.showToast('An unexpected error occurred', 'error')
        } finally {
            this.isLoading = false
        }
    },
    
    // Clear form data
    clearForm() {
        this.password = ''
        this.errors = {}
        this.showPassword = false
    },
    
    // Toggle password visibility
    togglePasswordVisibility() {
        this.showPassword = !this.showPassword
    },
    
    // Handle input changes to clear errors
    clearError(field) {
        if (this.errors[field]) {
            delete this.errors[field]
        }
        if (this.errors.general) {
            delete this.errors.general
        }
    },
    
    // Handle Enter key press
    handleKeyPress(event) {
        if (event.key === 'Enter') {
            this.handleSubmit()
        }
    },
    
    // Get error message for field
    getError(field) {
        return this.errors[field] || ''
    },
    
    // Check if field has error
    hasError(field) {
        return !!this.errors[field]
    },
    
    // Get input class based on error state
    getInputClass(field) {
        const baseClass = 'input-mobile'
        return this.hasError(field) ? `${baseClass} input-error` : baseClass
    }
}))

// Export template for reference (template is embedded in index.html)
export const loginFormTemplate = `
<!-- Login form template is embedded in index.html -->
`
