#!/usr/bin/env node

/**
 * Development setup script for Teacher Client
 * Helps with initial setup and common development tasks
 */

const fs = require('fs')
const path = require('path')

console.log('🚀 Teacher Client Development Setup')
console.log('=====================================')

// Check if .env exists
const envPath = path.join(__dirname, '..', '.env')
if (!fs.existsSync(envPath)) {
    console.log('📝 Creating .env file from .env.example...')
    const envExamplePath = path.join(__dirname, '..', '.env.example')
    if (fs.existsSync(envExamplePath)) {
        fs.copyFileSync(envExamplePath, envPath)
        console.log('✅ .env file created')
    } else {
        console.log('❌ .env.example not found')
    }
} else {
    console.log('✅ .env file already exists')
}

// Check if node_modules exists
const nodeModulesPath = path.join(__dirname, '..', 'node_modules')
if (!fs.existsSync(nodeModulesPath)) {
    console.log('📦 Please run "npm install" to install dependencies')
} else {
    console.log('✅ Dependencies installed')
}

// Create placeholder icons if they don't exist
const publicPath = path.join(__dirname, '..', 'public')
const icon192Path = path.join(publicPath, 'icon-192.png')
const icon512Path = path.join(publicPath, 'icon-512.png')

if (!fs.existsSync(icon192Path) || !fs.existsSync(icon512Path)) {
    console.log('🎨 Creating placeholder icons...')
    console.log('   Note: Replace these with actual app icons for production')
    
    // Create simple placeholder files (you would replace these with actual icons)
    if (!fs.existsSync(icon192Path)) {
        fs.writeFileSync(icon192Path, '')
        console.log('   📱 Created placeholder icon-192.png')
    }
    
    if (!fs.existsSync(icon512Path)) {
        fs.writeFileSync(icon512Path, '')
        console.log('   📱 Created placeholder icon-512.png')
    }
}

console.log('')
console.log('🎯 Next Steps:')
console.log('1. Update .env with your Laravel backend URL')
console.log('2. Run "npm run dev" to start development server')
console.log('3. Replace placeholder icons with actual app icons')
console.log('4. Test on mobile devices for best experience')
console.log('')
console.log('📚 Documentation: See README.md for detailed setup instructions')
console.log('🐛 Issues: Check browser console for any errors')
console.log('')
console.log('Happy coding! 🎉')
