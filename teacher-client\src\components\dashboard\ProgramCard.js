import Alpine from 'alpinejs'

Alpine.data('programCard', () => ({
    // Program data
    program: null,
    
    init() {
        // Component initialization
    },
    
    setProgram(program) {
        this.program = program
    },
    
    // Navigation
    viewProgram() {
        if (this.program) {
            this.$store.app.navigateTo('program', { id: this.program.id })
        }
    },
    
    // Utility methods
    formatDate(date) {
        if (!date) return 'N/A'
        return new Date(date).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        })
    },
    
    getStatusBadge() {
        if (!this.program) return 'badge-ghost'
        return this.program.active ? 'badge-success' : 'badge-error'
    },
    
    getStatusText() {
        if (!this.program) return 'Unknown'
        return this.program.active ? 'Active' : 'Inactive'
    },
    
    getProgressPercentage() {
        if (!this.program || !this.program.total_books || this.program.total_books === 0) {
            return 0
        }
        return Math.round((this.program.completed_books / this.program.total_books) * 100)
    },
    
    hasTeams() {
        return this.program && this.program.team_count > 0
    },
    
    hasBooks() {
        return this.program && this.program.book_count > 0
    },
    
    hasStudents() {
        return this.program && this.program.student_count > 0
    },
    
    getTeamCountText() {
        const count = this.program?.team_count || 0
        return count === 1 ? '1 Team' : `${count} Teams`
    },
    
    getStudentCountText() {
        const count = this.program?.student_count || 0
        return count === 1 ? '1 Student' : `${count} Students`
    },
    
    getBookCountText() {
        const count = this.program?.book_count || 0
        return count === 1 ? '1 Book' : `${count} Books`
    }
}))

// Export template for reference (template is embedded in index.html)
export const programCardTemplate = `
<!-- Program card template is embedded in the dashboard -->
`
