<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Role;
use App\Models\Term;
use App\Models\TermUser;
use Illuminate\Auth\Access\Response;

class UserPolicy
{
    /**
     * Determine whether the user can view any models.
     * This controls access to the resource index page.
     */
    public function viewAny(User $user): bool
    {
        // System admins, school admins, and teachers can access user management
        // Students cannot access the user management at all
        return $user->isSystemAdmin() ||
               $user->isSchoolAdmin() ||
               $user->isTeacher();
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, User $model): bool
    {
        // System admins can view all users
        if ($user->isSystemAdmin()) {
            return true;
        }

        // School admins can view users they can manage (students and teachers)
        if ($user->isSchoolAdmin()) {
            return $model->role->level > $user->role->level;
        }

        // Teachers can only view students in their assigned classes
        if ($user->isTeacher()) {
            return $this->canTeacherAccessStudent($user, $model);
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        // System admins, school admins, and teachers can create users
        // Teachers can only create students
        return $user->isSystemAdmin() ||
               $user->isSchoolAdmin() ||
               $user->isTeacher();
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, User $model): bool
    {
        // System admins can update all users
        if ($user->isSystemAdmin()) {
            return true;
        }

        // School admins can update users they can manage
        if ($user->isSchoolAdmin()) {
            return $model->role->level > $user->role->level;
        }

        // Teachers can only update students in their assigned classes
        if ($user->isTeacher()) {
            return $this->canTeacherAccessStudent($user, $model);
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, User $model): bool
    {
        // Only system admins and group school admins can delete users
        // Teachers cannot delete students
        return $user->isSystemAdmin() ||
               $user->hasRoleLevel(Role::LEVEL_GROUP_SCHOOL_ADMIN);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, User $model): bool
    {
        return $user->isSystemAdmin();
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, User $model): bool
    {
        return $user->isSystemAdmin();
    }

    /**
     * Determine whether the user can perform mass delete.
     */
    public function massDelete(User $user): bool
    {
        return $user->isSystemAdmin();
    }

    /**
     * Check if a teacher can access a specific student
     */
    private function canTeacherAccessStudent(User $teacher, User $student): bool
    {
        // Only allow access to students
        if (!$student->isStudent()) {
            return false;
        }

        $activeTerm = Term::getActiveTerm();
        if (!$activeTerm) {
            return false;
        }

        // Get teacher's assigned classes
        $teacherClassIds = TermUser::where('user_id', $teacher->id)
            ->where('term_id', $activeTerm->id)
            ->where('active', true)
            ->whereHas('role', function ($q) {
                $q->where('level', Role::LEVEL_TEACHER);
            })
            ->pluck('class_id')
            ->filter()
            ->toArray();

        if (empty($teacherClassIds)) {
            return false;
        }

        // Check if student is in any of teacher's classes
        return TermUser::where('user_id', $student->id)
            ->where('term_id', $activeTerm->id)
            ->where('active', true)
            ->whereIn('class_id', $teacherClassIds)
            ->whereHas('role', function ($q) {
                $q->where('level', Role::LEVEL_STUDENT);
            })
            ->exists();
    }
}
