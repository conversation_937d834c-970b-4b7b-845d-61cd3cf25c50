import axios from 'axios'
import { storageService } from './storage.js'

class ApiService {
    constructor() {
        this.client = null
        this.baseURL = ''
    }
    
    init(config) {
        this.baseURL = config.baseURL
        
        this.client = axios.create({
            baseURL: config.baseURL,
            timeout: config.timeout || 10000,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        
        // Request interceptor to add auth token
        this.client.interceptors.request.use(
            (config) => {
                const token = storageService.getToken()
                if (token) {
                    config.headers.Authorization = `Bearer ${token}`
                }
                return config
            },
            (error) => {
                return Promise.reject(error)
            }
        )
        
        console.log('🔗 API Service initialized with base URL:', config.baseURL)
    }
    
    setupAuthInterceptors(onUnauthorized) {
        // Response interceptor to handle auth errors
        this.client.interceptors.response.use(
            (response) => response,
            (error) => {
                if (error.response?.status === 401) {
                    console.log('🔒 Unauthorized request, logging out...')
                    onUnauthorized()
                }
                return Promise.reject(error)
            }
        )
    }
    
    // Generic request methods
    async get(url, config = {}) {
        try {
            const response = await this.client.get(url, config)
            return this.handleResponse(response)
        } catch (error) {
            throw this.handleError(error)
        }
    }
    
    async post(url, data = {}, config = {}) {
        try {
            const response = await this.client.post(url, data, config)
            return this.handleResponse(response)
        } catch (error) {
            throw this.handleError(error)
        }
    }
    
    async put(url, data = {}, config = {}) {
        try {
            const response = await this.client.put(url, data, config)
            return this.handleResponse(response)
        } catch (error) {
            throw this.handleError(error)
        }
    }
    
    async delete(url, config = {}) {
        try {
            const response = await this.client.delete(url, config)
            return this.handleResponse(response)
        } catch (error) {
            throw this.handleError(error)
        }
    }
    
    handleResponse(response) {
        return response.data
    }
    
    handleError(error) {
        console.error('🚨 API Error:', error)
        
        if (error.response) {
            // Server responded with error status
            const { status, data } = error.response
            return {
                status,
                message: data.message || data.error || 'An error occurred',
                errors: data.errors || {},
                data: data
            }
        } else if (error.request) {
            // Network error
            return {
                status: 0,
                message: 'Network error. Please check your connection.',
                errors: {},
                data: null
            }
        } else {
            // Other error
            return {
                status: 0,
                message: error.message || 'An unexpected error occurred',
                errors: {},
                data: null
            }
        }
    }
    
    // Authentication endpoints
    async login(credentials) {
        return this.post('/auth/login', credentials)
    }
    
    async logout() {
        return this.post('/auth/logout')
    }
    
    async getCurrentUser() {
        return this.get('/auth/user')
    }
    
    async refreshToken() {
        return this.post('/auth/refresh')
    }
    
    // Student-specific endpoints
    async getStudentPrograms() {
        return this.get('/student/programs')
    }
    
    async getStudentProgramDetail(programId) {
        return this.get(`/student/programs/${programId}`)
    }
    
    async getStudentProfile() {
        return this.get('/student/profile')
    }
    
    async getStudentAchievements() {
        return this.get('/student/achievements')
    }
    
    async getStudentProgress(programId) {
        return this.get(`/student/programs/${programId}/progress`)
    }
    
    async getStudentBooks(programId) {
        return this.get(`/student/programs/${programId}/books`)
    }
    
    async getStudentTasks(programId) {
        return this.get(`/student/programs/${programId}/tasks`)
    }
    
    // Reading activity endpoints
    async startReading(bookId) {
        return this.post(`/student/books/${bookId}/start`)
    }
    
    async completeReading(bookId, data = {}) {
        return this.post(`/student/books/${bookId}/complete`, data)
    }
    
    async submitTask(taskId, data = {}) {
        return this.post(`/student/tasks/${taskId}/submit`, data)
    }
    
    async updateReadingProgress(bookId, progress) {
        return this.put(`/student/books/${bookId}/progress`, { progress })
    }
    
    // Game data endpoints
    async getGameData() {
        return this.get('/student/game-data')
    }
    
    async updatePoints(points) {
        return this.post('/student/points', { points })
    }
    
    async unlockAchievement(achievementId) {
        return this.post(`/student/achievements/${achievementId}/unlock`)
    }
    
    // Active term information
    async getActiveTerm() {
        return this.get('/student/active-term')
    }
    
    // Team information
    async getStudentTeam(programId) {
        return this.get(`/student/programs/${programId}/team`)
    }
    
    async getTeamMembers(teamId) {
        return this.get(`/student/teams/${teamId}/members`)
    }
    
    // Story/Character endpoints
    async getStoryProgress(storyId) {
        return this.get(`/student/stories/${storyId}/progress`)
    }
    
    async getCharacterData(characterId) {
        return this.get(`/student/characters/${characterId}`)
    }
    
    async updateCharacterProgress(characterId, data) {
        return this.put(`/student/characters/${characterId}/progress`, data)
    }
    
    // Leaderboard endpoints
    async getLeaderboard(programId, type = 'points') {
        return this.get(`/student/programs/${programId}/leaderboard?type=${type}`)
    }
    
    async getClassRanking(programId) {
        return this.get(`/student/programs/${programId}/ranking`)
    }
}

export const apiService = new ApiService()
