<!DOCTYPE html>
<html lang="en" data-theme="student">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <meta name="theme-color" content="#d946ef">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Reading Quest">
    
    <title>Reading Quest - Student Portal</title>
    
    <!-- Preconnect to improve performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Fredoka font for gamified feel -->
    <link href="https://fonts.googleapis.com/css2?family=Fredoka:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- PWA manifest -->
    <link rel="manifest" href="/manifest.json">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    
    <!-- iOS specific meta tags -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    
    <!-- Prevent zoom on input focus (iOS) -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
</head>
<body class="bg-gradient-to-br from-purple-100 via-pink-50 to-orange-100 font-game antialiased">
    <!-- App container -->
    <div id="app" x-data="app" x-init="init()" class="min-h-screen-safe">
        <!-- Loading spinner shown before Alpine.js initializes -->
        <div id="initial-loader" class="flex items-center justify-center min-h-screen">
            <div class="text-center">
                <div class="loading loading-spinner loading-lg text-primary mb-4"></div>
                <p class="text-primary font-semibold animate-pulse">Loading Reading Quest...</p>
            </div>
        </div>
        
        <!-- App Loading State -->
        <div x-show="isLoading" class="flex items-center justify-center min-h-screen bg-gradient-to-br from-purple-100 via-pink-50 to-orange-100">
            <div class="text-center">
                <div class="relative">
                    <!-- Animated book icon -->
                    <div class="w-20 h-20 mx-auto mb-6 relative">
                        <div class="absolute inset-0 bg-primary rounded-lg animate-pulse"></div>
                        <div class="absolute inset-2 bg-white rounded flex items-center justify-center">
                            <svg class="w-8 h-8 text-primary animate-bounce" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                    </div>
                </div>
                <h2 class="text-2xl font-bold text-primary mb-2">Reading Quest</h2>
                <p class="text-primary/70 animate-pulse">Preparing your adventure...</p>
            </div>
        </div>
        
        <!-- Login Screen -->
        <div x-show="!isLoading && !isAuthenticated && currentRoute === 'login'">
            <div x-data="loginForm" class="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-100 via-pink-50 to-orange-100 px-4">
                <!-- Floating decorative elements -->
                <div class="absolute inset-0 overflow-hidden pointer-events-none">
                    <div class="absolute top-20 left-10 w-8 h-8 bg-yellow-300 rounded-full animate-float opacity-60"></div>
                    <div class="absolute top-40 right-16 w-6 h-6 bg-pink-300 rounded-full animate-bounce-slow opacity-60"></div>
                    <div class="absolute bottom-32 left-20 w-10 h-10 bg-blue-300 rounded-full animate-pulse-slow opacity-60"></div>
                    <div class="absolute bottom-20 right-10 w-4 h-4 bg-green-300 rounded-full animate-wiggle opacity-60"></div>
                </div>
                
                <div class="card w-full max-w-md bg-white/90 backdrop-blur-sm shadow-2xl border-2 border-primary/20">
                    <div class="card-body">
                        <!-- Header with game-style logo -->
                        <div class="text-center mb-8">
                            <div class="relative inline-block">
                                <h1 class="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary">
                                    Reading Quest
                                </h1>
                                <div class="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full animate-sparkle"></div>
                            </div>
                            <p class="text-base-content/70 mt-3 font-medium">Start your reading adventure!</p>
                        </div>
                        
                        <!-- Error Alert -->
                        <div x-show="errors.general" x-transition class="alert alert-error mb-4 bg-red-100 border-red-300">
                            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span x-text="errors.general"></span>
                        </div>
                        
                        <!-- Login Form -->
                        <form @submit.prevent="handleSubmit" class="space-y-6">
                            <!-- Email Field -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text font-semibold text-primary">Email</span>
                                </label>
                                <div class="relative">
                                    <input 
                                        x-ref="emailInput"
                                        x-model="email"
                                        @input="clearError('email')"
                                        @keypress="handleKeyPress"
                                        type="email"
                                        placeholder="Enter your email"
                                        :class="getInputClass('email')"
                                        :disabled="isLoading"
                                        autocomplete="email"
                                        required
                                        class="input input-bordered w-full text-base bg-white/80 border-2 focus:border-primary focus:bg-white transition-all duration-200"
                                    />
                                    <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                        <svg class="w-5 h-5 text-primary/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"/>
                                        </svg>
                                    </div>
                                </div>
                                <label x-show="hasError('email')" class="label">
                                    <span class="label-text-alt text-error font-medium" x-text="getError('email')"></span>
                                </label>
                            </div>
                            
                            <!-- Password Field -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text font-semibold text-primary">Password</span>
                                </label>
                                <div class="relative">
                                    <input 
                                        x-model="password"
                                        @input="clearError('password')"
                                        @keypress="handleKeyPress"
                                        :type="showPassword ? 'text' : 'password'"
                                        placeholder="Enter your password"
                                        :class="getInputClass('password')"
                                        :disabled="isLoading"
                                        autocomplete="current-password"
                                        required
                                        class="input input-bordered w-full text-base bg-white/80 border-2 focus:border-primary focus:bg-white transition-all duration-200"
                                    />
                                    <button 
                                        type="button"
                                        @click="togglePasswordVisibility"
                                        class="absolute right-3 top-1/2 transform -translate-y-1/2 btn btn-ghost btn-sm btn-circle hover:bg-primary/10"
                                        :disabled="isLoading"
                                    >
                                        <svg x-show="!showPassword" class="w-5 h-5 text-primary/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                        </svg>
                                        <svg x-show="showPassword" class="w-5 h-5 text-primary/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                                        </svg>
                                    </button>
                                </div>
                                <label x-show="hasError('password')" class="label">
                                    <span class="label-text-alt text-error font-medium" x-text="getError('password')"></span>
                                </label>
                            </div>
                            
                            <!-- Remember Me -->
                            <div class="form-control">
                                <label class="label cursor-pointer justify-start gap-3">
                                    <input 
                                        x-model="rememberMe"
                                        type="checkbox" 
                                        class="checkbox checkbox-primary checkbox-sm"
                                        :disabled="isLoading"
                                    />
                                    <span class="label-text font-medium">Remember me</span>
                                </label>
                            </div>
                            
                            <!-- Submit Button -->
                            <button 
                                type="submit"
                                class="btn w-full text-white font-bold text-lg py-4 bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 border-0 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
                                :disabled="isLoading"
                                :class="{ 'loading': isLoading }"
                            >
                                <span x-show="!isLoading" class="flex items-center gap-2">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"/>
                                    </svg>
                                    Start Adventure
                                </span>
                                <span x-show="isLoading">Logging in...</span>
                            </button>
                        </form>
                        
                        <!-- Footer -->
                        <div class="text-center mt-6 text-sm text-base-content/60">
                            <p>Need help? Ask your teacher!</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main App Layout -->
        <div x-show="!isLoading && isAuthenticated" x-data="appLayout" class="min-h-screen bg-gradient-to-br from-purple-100 via-pink-50 to-orange-100">
            <!-- Game-style header -->
            <header class="bg-gradient-to-r from-primary to-secondary text-white sticky top-0 z-40 safe-area-top shadow-lg">
                <div class="container-mobile">
                    <div class="navbar px-0">
                        <div class="navbar-start">
                            <button 
                                @click="toggleSidebar"
                                class="btn btn-ghost btn-circle text-white hover:bg-white/20"
                            >
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                                </svg>
                            </button>
                        </div>
                        
                        <div class="navbar-center">
                            <h1 class="text-xl font-bold">Reading Quest</h1>
                        </div>
                        
                        <div class="navbar-end">
                            <!-- Points display -->
                            <div class="flex items-center gap-2 bg-white/20 rounded-full px-3 py-1">
                                <svg class="w-5 h-5 text-yellow-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                                <span class="font-bold text-sm" x-text="userPoints || 0"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Celebration Modal -->
            <div x-show="showCelebration" x-transition class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
                <div class="bg-white rounded-2xl p-8 text-center max-w-sm w-full animate-bounce-in">
                    <div class="text-6xl mb-4" x-text="celebrationType === 'levelup' ? '🎊' : '🏆'"></div>
                    <h3 class="text-2xl font-bold text-primary mb-2" x-text="getCelebrationMessage()"></h3>
                    <p class="text-base-content/70 mb-6" x-text="celebrationType === 'levelup' ? 'You leveled up!' : 'Achievement unlocked!'"></p>
                    <button @click="closeCelebration" class="btn btn-primary">Awesome!</button>
                </div>
            </div>

            <!-- Main Content -->
            <main x-ref="mainContent" class="container-mobile min-h-screen pb-20 scroll-smooth-mobile">
                <!-- Pull to refresh indicator -->
                <div x-ref="pullIndicator" class="fixed top-16 left-1/2 transform -translate-x-1/2 -translate-y-full bg-primary/90 text-white px-4 py-2 rounded-full shadow-lg z-40 transition-all duration-300">
                    <div class="flex items-center gap-2">
                        <div class="loading loading-spinner loading-sm"></div>
                        <span class="text-sm font-semibold">Pull to refresh</span>
                    </div>
                </div>

                <!-- Dashboard Route -->
                <div x-show="currentRoute === 'dashboard'" x-data="dashboard" class="py-6 space-y-6">
                    <!-- Loading State -->
                    <div x-show="isLoading" class="space-y-4">
                        <div class="skeleton-mobile h-24 w-full rounded-2xl"></div>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="skeleton-mobile h-20 rounded-xl"></div>
                            <div class="skeleton-mobile h-20 rounded-xl"></div>
                            <div class="skeleton-mobile h-20 rounded-xl"></div>
                            <div class="skeleton-mobile h-20 rounded-xl"></div>
                        </div>
                        <div class="space-y-3">
                            <div class="skeleton-mobile h-32 w-full rounded-2xl"></div>
                            <div class="skeleton-mobile h-32 w-full rounded-2xl"></div>
                        </div>
                    </div>

                    <!-- Error State -->
                    <div x-show="error && !isLoading" class="alert alert-error bg-red-100 border-red-300 rounded-2xl">
                        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span x-text="error"></span>
                        <button @click="refreshData" class="btn btn-sm btn-outline">Try Again</button>
                    </div>

                    <!-- Dashboard Content -->
                    <div x-show="!isLoading && !error" class="space-y-6">
                        <!-- Welcome Section -->
                        <div class="card-game p-6 relative overflow-hidden">
                            <!-- Background decoration -->
                            <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-primary/20 to-transparent rounded-full -mr-16 -mt-16"></div>
                            <div class="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-secondary/20 to-transparent rounded-full -ml-12 -mb-12"></div>

                            <div class="relative z-10">
                                <h2 class="text-2xl font-bold text-game-gradient mb-2" x-text="getGreeting()"></h2>
                                <p class="text-base-content/70 mb-4" x-text="getMotivationalMessage()"></p>

                                <!-- Quick Stats -->
                                <div class="flex items-center gap-4 text-sm">
                                    <div class="flex items-center gap-1">
                                        <span class="text-2xl">⭐</span>
                                        <span class="font-semibold" x-text="getUserPoints()"></span>
                                    </div>
                                    <div class="flex items-center gap-1">
                                        <span class="text-2xl">📚</span>
                                        <span class="font-semibold" x-text="getBooksRead()"></span>
                                    </div>
                                    <div x-show="getUserStreak() > 0" class="flex items-center gap-1" @click="celebrateStreak">
                                        <span class="text-2xl">🔥</span>
                                        <span class="font-semibold" x-text="getUserStreak()"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Level Progress -->
                        <div class="card-game p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-bold text-primary">Level Progress</h3>
                                <div class="level-indicator" x-text="getUserLevel()"></div>
                            </div>

                            <div class="progress-game mb-2">
                                <div class="progress-game-fill" :style="`width: ${getProgressToNextLevel().percentage}%`"></div>
                            </div>

                            <div class="flex justify-between text-sm text-base-content/70">
                                <span x-text="`${getProgressToNextLevel().current} XP`"></span>
                                <span x-text="`${getProgressToNextLevel().required} XP to level ${getUserLevel() + 1}`"></span>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="grid grid-cols-2 gap-4">
                            <button @click="startQuickReading" class="card-game p-4 text-center hover:scale-105 transition-transform">
                                <div class="text-3xl mb-2">📖</div>
                                <div class="font-semibold text-primary">Start Reading</div>
                                <div class="text-xs text-base-content/70">Continue your adventure</div>
                            </button>

                            <button @click="goToAchievements" class="card-game p-4 text-center hover:scale-105 transition-transform">
                                <div class="text-3xl mb-2">🏆</div>
                                <div class="font-semibold text-primary">Achievements</div>
                                <div class="text-xs text-base-content/70">See your progress</div>
                            </button>
                        </div>

                        <!-- Active Programs -->
                        <div x-show="hasActivePrograms()">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-bold text-primary">Your Programs</h3>
                                <span class="badge badge-primary" x-text="getActivePrograms().length"></span>
                            </div>

                            <div class="space-y-3">
                                <template x-for="program in getActivePrograms()" :key="program.id">
                                    <div x-data="programCard" x-init="setProgram(program)" @click="viewProgram()" class="card-game p-4 cursor-pointer hover:scale-105 transition-transform">
                                        <div class="flex items-start justify-between mb-3">
                                            <div class="flex-1 min-w-0">
                                                <h4 class="font-bold text-base-content truncate" x-text="program?.name || 'Program'"></h4>
                                                <p class="text-sm text-base-content/70 mt-1" x-text="program?.description || 'Join this reading adventure!'"></p>
                                            </div>
                                            <div class="ml-3 flex-shrink-0">
                                                <span class="badge badge-success badge-sm">Active</span>
                                            </div>
                                        </div>

                                        <!-- Progress Bar -->
                                        <div x-show="program?.progress" class="mb-3">
                                            <div class="flex items-center justify-between text-xs text-base-content/70 mb-1">
                                                <span>Progress</span>
                                                <span x-text="(program?.progress || 0) + '%'"></span>
                                            </div>
                                            <div class="progress-game">
                                                <div class="progress-game-fill" :style="`width: ${program?.progress || 0}%`"></div>
                                            </div>
                                        </div>

                                        <!-- Stats -->
                                        <div class="flex items-center justify-between text-sm">
                                            <div class="flex items-center gap-4">
                                                <div x-show="program?.book_count" class="flex items-center gap-1 text-base-content/70">
                                                    <span class="text-lg">📚</span>
                                                    <span x-text="program.book_count + ' books'"></span>
                                                </div>
                                                <div x-show="program?.points" class="flex items-center gap-1 text-base-content/70">
                                                    <span class="text-lg">⭐</span>
                                                    <span x-text="program.points + ' pts'"></span>
                                                </div>
                                            </div>

                                            <svg class="w-5 h-5 text-base-content/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                            </svg>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>

                        <!-- Recent Activity -->
                        <div x-show="hasRecentActivity()">
                            <h3 class="text-lg font-bold text-primary mb-4">Recent Activity</h3>

                            <div class="space-y-3">
                                <template x-for="activity in recentActivity" :key="activity.title">
                                    <div class="card-game p-4">
                                        <div class="flex items-start gap-3">
                                            <div class="text-2xl" x-text="activity.icon"></div>
                                            <div class="flex-1 min-w-0">
                                                <h4 class="font-semibold text-base-content" x-text="activity.title"></h4>
                                                <p class="text-sm text-base-content/70" x-text="activity.description"></p>
                                                <p class="text-xs text-base-content/50 mt-1" x-text="activity.time"></p>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>

                        <!-- Empty State -->
                        <div x-show="!hasPrograms()" class="text-center py-12">
                            <div class="text-6xl mb-4">📚</div>
                            <h3 class="text-xl font-bold text-base-content/70 mb-2">Ready to Start Reading?</h3>
                            <p class="text-base-content/50 mb-6">Join a reading program to begin your adventure!</p>
                            <button @click="goToLibrary" class="btn-game">Explore Library</button>
                        </div>
                    </div>
                </div>

                <!-- Program Detail Route -->
                <div x-show="currentRoute === 'program'" x-data="programDetail">
                    <!-- Program detail content will be loaded here -->
                </div>
            </main>
            
            <!-- Game-style bottom navigation -->
            <nav x-data="bottomNav" class="fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-sm border-t-2 border-primary/20 safe-area-bottom z-50">
                <div class="flex justify-around items-center py-2">
                    <button 
                        @click="navigateTo('dashboard')"
                        class="flex flex-col items-center gap-1 p-3 rounded-xl transition-all duration-200"
                        :class="isActive('dashboard') ? 'text-primary bg-primary/10 scale-110' : 'text-base-content/60 hover:text-primary hover:bg-primary/5'"
                    >
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6a2 2 0 01-2 2H10a2 2 0 01-2-2V5z" />
                        </svg>
                        <span class="text-xs font-semibold">Home</span>
                    </button>
                    
                    <button 
                        @click="navigateTo('library')"
                        class="flex flex-col items-center gap-1 p-3 rounded-xl transition-all duration-200"
                        :class="isActive('library') ? 'text-primary bg-primary/10 scale-110' : 'text-base-content/60 hover:text-primary hover:bg-primary/5'"
                    >
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                        <span class="text-xs font-semibold">Library</span>
                    </button>
                    
                    <button 
                        @click="navigateTo('achievements')"
                        class="flex flex-col items-center gap-1 p-3 rounded-xl transition-all duration-200"
                        :class="isActive('achievements') ? 'text-primary bg-primary/10 scale-110' : 'text-base-content/60 hover:text-primary hover:bg-primary/5'"
                    >
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                        </svg>
                        <span class="text-xs font-semibold">Badges</span>
                    </button>
                    
                    <button 
                        @click="navigateTo('profile')"
                        class="flex flex-col items-center gap-1 p-3 rounded-xl transition-all duration-200"
                        :class="isActive('profile') ? 'text-primary bg-primary/10 scale-110' : 'text-base-content/60 hover:text-primary hover:bg-primary/5'"
                    >
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        <span class="text-xs font-semibold">Profile</span>
                    </button>
                </div>
            </nav>
        </div>
    </div>

    <!-- Scripts -->
    <script type="module" src="/src/main.js"></script>
</body>
</html>
