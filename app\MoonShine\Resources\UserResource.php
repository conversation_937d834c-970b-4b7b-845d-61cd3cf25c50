<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Log;
use MoonShine\Laravel\Enums\Ability;
use App\Models\Term;
use App\Models\TermUser;
use Illuminate\Contracts\Database\Eloquent\Builder;

use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Components\Tabs;
use MoonShine\UI\Components\Tabs\Tab;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Email;
use MoonShine\UI\Fields\Password;
use MoonShine\UI\Fields\PasswordRepeat;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;


#[Icon('users')]
class UserResource extends BaseResource
{
    // TODO: Teachers can only see their own students

    protected string $model = User::class;

    protected string $column = 'name';

    protected array $with = ['role', 'creator', 'updater'];

    protected bool $withPolicy = true;

    public function getTitle(): string
    {
        $user = auth('moonshine')->user();
        if ($user instanceof User && $user->isTeacher()) {
            return __('admin.my_students');
        }
        return __('admin.users');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            Text::make(__('admin.name'), 'name')
                ->sortable(),

            Text::make(__('admin.title'), 'title')
                ->sortable(),
            
            Email::make(__('admin.email'), 'email')
                ->sortable(),
            
            BelongsTo::make(__('admin.role'), 'role', 
                formatted: fn(Role $role) => $role->name)
                ->sortable(),
            
            Text::make(__('admin.role_level'), 'role.level')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Tabs::make([
                    Tab::make(__('admin.main_information'), [
                        Flex::make([
                            Text::make(__('admin.name'), 'name')
                                ->required()
                                ->placeholder(__('admin.enter_name')),

                    Text::make(__('admin.title'), 'title')
                        ->placeholder(__('admin.enter_title')),
                            
                            Email::make(__('admin.email'), 'email')
                                ->required()
                                ->placeholder(__('admin.enter_email')),
                        ]),
                        
                        BelongsTo::make(__('admin.role'), 'role', 
                            formatted: fn(Role $role) => $role->name,
                            resource: RoleResource::class)
                            ->required()
                            ->placeholder(__('admin.select_role'))
                            ->valuesQuery(function ($query) {
                                $user = auth('moonshine')->user();
                                if ($user instanceof User) {
                                    if ($user->isTeacher()) {
                                        // Teachers can only assign student role
                                        return $query->where('level', Role::LEVEL_STUDENT);
                                    } elseif (!$user->isSystemAdmin()) {
                                        // Non-system admins can only assign roles they can manage
                                        return $query->where('level', '>', $user->role->level);
                                    }
                                }
                                return $query;
                            }),
                    ]),
                    
                    Tab::make(__('admin.password'), [
                        Password::make(__('admin.password'), 'password')
                            ->customAttributes(['autocomplete' => 'new-password'])
                            ->required($this->isCreateFormPage()),
                        
                        PasswordRepeat::make(__('admin.password_repeat'), 'password_confirmation')
                            ->customAttributes(['autocomplete' => 'confirm-password'])
                            ->required($this->isCreateFormPage()),
                    ]),
                ]),
            ]),
            
            
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.name'), 'name'),
            Text::make(__('admin.title'), 'title'),
            Email::make(__('admin.email'), 'email'),
            Text::make(__('admin.role'), 'role.name'),
            Text::make(__('admin.role_level'), 'role.level'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'title' => ['nullable', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $item?->id],
            'role_id' => ['required', 'exists:roles,id'],
            ...parent::getCommonRules($item),
        ];

        // Password rules
        if (!$item) { // Creating new user
            $rules['password'] = ['required', 'string', 'min:8', 'confirmed'];
        } elseif (request()->filled('password')) {
            $rules['password'] = ['string', 'min:8', 'confirmed'];
        }

        return $rules;
    }

    protected function isCan(Ability $ability): bool
    {
        $user = auth('moonshine')->user();

        // Debug logging
        Log::info('UserResource isCan called', [
            'ability' => $ability->value,
            'user_id' => $user?->id,
            'user_name' => $user?->name,
            'user_role' => $user?->role?->name,
            'is_teacher' => $user instanceof User ? $user->isTeacher() : false,
        ]);

        // If not a valid user, deny access
        if (!$user instanceof User) {
            return false;
        }

        // Handle viewAny ability for filtering records on index page
        if ($ability === Ability::VIEW_ANY) {
            // System admins, school admins, and teachers can view users
            if ($user->isSystemAdmin() || $user->isSchoolAdmin() || $user->isTeacher()) {
                return true;
            }
            return false;
        }

        // For other abilities, use the parent logic (which will use the policy)
        return parent::isCan($ability);
    }


    /**
     * Modify query builder to filter records based on user role.
     * Teachers can only see students in their assigned classes.
     */
/*
    protected function modifyQueryBuilder(Builder $builder): Builder
    {
        $user = auth('moonshine')->user();

        if (!$user instanceof User) {
            return $builder->where('id', '=', 0);
        }

        if ($user->isTeacher()) {
            Log::info('Applying teacher filtering for user', ['user_id' => $user->id]);

            // Teachers can only see students in their assigned classes
            $activeTerm = Term::getActiveTerm();
            if (!$activeTerm) {
                Log::info('No active term found, returning empty result');
                return $builder->where('id', '=', 0);
            }

            // Get teacher's assigned classes from term_users
            $teacherClassIds = TermUser::where('user_id', $user->id)
                ->where('term_id', $activeTerm->id)
                ->where('active', true)
                ->whereHas('role', function ($roleQuery) {
                    $roleQuery->where('level', Role::LEVEL_TEACHER);
                })
                ->pluck('class_id')
                ->filter()
                ->toArray();

            Log::info('Teacher assigned classes', ['class_ids' => $teacherClassIds]);

            if (empty($teacherClassIds)) {
                Log::info('Teacher has no assigned classes, returning empty result');
                return $builder->where('id', '=', 0);
            }

            // Filter users to only show students in teacher's classes
            return $builder->whereHas('termUsers', function ($termQuery) use ($activeTerm, $teacherClassIds) {
                $termQuery->where('term_id', $activeTerm->id)
                  ->where('active', true)
                  ->whereIn('class_id', $teacherClassIds)
                  ->whereHas('role', function ($roleQuery) {
                      $roleQuery->where('level', Role::LEVEL_STUDENT);
                  });
            });
        } elseif (!$user->isSystemAdmin()) {
            // School admins can only see users they can manage
            return $builder->whereHas('role', function ($roleQuery) use ($user) {
                $roleQuery->where('level', '>', $user->role->level);
            });
        }

        // System admins see all users
        return $builder;
    }

*/

    protected function checkCreatePermission($user): bool
    {
        // System admins, school admins, and teachers can create users
        // Teachers can only create students
        return $user->isSystemAdmin() ||
               $user->hasRoleLevel(Role::LEVEL_SCHOOL_ADMIN) ||
               $user->isTeacher();
    }

    protected function checkUpdatePermission($user): bool
    {
        // System admins, school admins, and teachers can update users
        // Teachers can only update students in their classes
        return $user->isSystemAdmin() ||
               $user->hasRoleLevel(Role::LEVEL_SCHOOL_ADMIN) ||
               $user->isTeacher();
    }

    protected function checkDeletePermission($user): bool
    {
        // Only system admins can delete users
        // Teachers cannot delete students
        return $user->isSystemAdmin();
    }

    protected function getSearchFields(): array
    {
        return ['name', 'email'];
    }
}
