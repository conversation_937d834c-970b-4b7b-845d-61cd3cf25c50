<?php

namespace App\MoonShine\Resources;

use App\Models\StoryCharacterStage;
use App\Models\StoryCharacter;
use App\Models\StoryRule;
use App\Models\Role;
use MoonShine\Support\Attributes\Icon;
use MoonShine\UI\Fields\{Text, Number, Image, Json};
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Components\Tabs;
use MoonShine\UI\Components\Tabs\Tab;

#[Icon('star')]
class StoryCharacterStageResource extends BaseResource
{
    protected string $model = StoryCharacterStage::class;

    protected string $column = 'full_name';

    protected array $with = ['character', 'unlockRule', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.story_character_stages');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            BelongsTo::make(__('admin.character'), 'character', 
                formatted: fn(StoryCharacter $character) => $character->name,
                resource: StoryCharacterResource::class)
                ->sortable(),
            
            Number::make(__('admin.stage_number'), 'stage_number')
                ->sortable(),
            
            Text::make(__('admin.name'), 'name')
                ->sortable(),
            
            Text::make(__('admin.full_name'), 'full_name'),
            
            Image::make(__('admin.image'), 'image'),
            
            BelongsTo::make(__('admin.unlock_rule'), 'unlockRule', 
                formatted: fn(?StoryRule $rule) => $rule?->description ?? 'No rule',
                resource: StoryRuleResource::class)
                ->nullable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Tabs::make([
                    Tab::make(__('admin.main_information'), [
                        BelongsTo::make(__('admin.character'), 'character', 
                            formatted: fn(StoryCharacter $character) => $character->name,
                            resource: StoryCharacterResource::class)
                            ->required()
                            ->placeholder(__('admin.select_character')),
                        
                        Flex::make([
                            Number::make(__('admin.stage_number'), 'stage_number')
                                ->required()
                                ->min(1)
                                ->placeholder(__('admin.enter_stage_number')),
                            
                            Text::make(__('admin.name'), 'name')
                                ->required()
                                ->placeholder(__('admin.enter_name')),
                        ]),
                        
                        Image::make(__('admin.image'), 'image')
                            ->required(),
                        
                        BelongsTo::make(__('admin.unlock_rule'), 'unlockRule', 
                            formatted: fn(?StoryRule $rule) => $rule?->description ?? 'No rule',
                            resource: StoryRuleResource::class)
                            ->nullable()
                            ->placeholder(__('admin.select_unlock_rule')),
                    ]),
                    
                    Tab::make(__('admin.abilities'), [
                        Json::make(__('admin.abilities'), 'abilities')
                            ->nullable()
                            ->hint('Optional special abilities for this stage'),
                    ]),
                    
                    Tab::make(__('admin.summary'), [
                        Text::make(__('admin.full_name'), 'full_name')
                            ->readonly(),
                        
                        Text::make(__('admin.abilities_text'), 'abilities_text')
                            ->readonly(),
                    ]),
                ]),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(__('admin.character'), 'character', 
                formatted: fn(StoryCharacter $character) => $character->name,
                resource: StoryCharacterResource::class),
            Number::make(__('admin.stage_number'), 'stage_number'),
            Text::make(__('admin.name'), 'name'),
            Text::make(__('admin.full_name'), 'full_name'),
            Image::make(__('admin.image'), 'image'),
            BelongsTo::make(__('admin.unlock_rule'), 'unlockRule', 
                formatted: fn(?StoryRule $rule) => $rule?->description ?? 'No rule',
                resource: StoryRuleResource::class),
            Json::make(__('admin.abilities'), 'abilities'),
            Text::make(__('admin.abilities_text'), 'abilities_text'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'character_id' => ['required', 'exists:story_characters,id'],
            'stage_number' => ['required', 'integer', 'min:1'],
            'name' => ['required', 'string', 'max:255'],
            'image' => ['required', 'string', 'max:255'],
            'unlock_rule_id' => ['nullable', 'exists:story_rules,id'],
            'abilities' => ['nullable', 'array'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function checkCreatePermission($user): bool
    {
        // System admins, group school admins, and school admins can create character stages
        return $user->isSystemAdmin() || 
               $user->hasRoleLevel(Role::LEVEL_GROUP_SCHOOL_ADMIN) ||
               $user->hasRoleLevel(Role::LEVEL_SCHOOL_ADMIN);
    }

    protected function checkUpdatePermission($user): bool
    {
        // System admins, group school admins, and school admins can update character stages
        return $user->isSystemAdmin() || 
               $user->hasRoleLevel(Role::LEVEL_GROUP_SCHOOL_ADMIN) ||
               $user->hasRoleLevel(Role::LEVEL_SCHOOL_ADMIN);
    }

    protected function checkDeletePermission($user): bool
    {
        // Only system admins and group school admins can delete character stages
        return $user->isSystemAdmin() || 
               $user->hasRoleLevel(Role::LEVEL_GROUP_SCHOOL_ADMIN);
    }

    protected function getSearchFields(): array
    {
        return ['name'];
    }

    protected function getDefaultSort(): array
    {
        return ['stage_number' => 'asc'];
    }
}
