@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom CSS for mobile-first design */

/* Base styles */
@layer base {
  html {
    font-size: 16px;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
  }
  
  body {
    font-family: 'Inter', system-ui, sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* Safe area handling for mobile devices */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .safe-area-left {
    padding-left: env(safe-area-inset-left);
  }
  
  .safe-area-right {
    padding-right: env(safe-area-inset-right);
  }
}

/* Component styles */
@layer components {
  /* Touch-friendly buttons */
  .btn-touch {
    @apply min-h-[44px] min-w-[44px] touch-manipulation;
  }
  
  /* Mobile-optimized form inputs */
  .input-mobile {
    @apply input input-bordered w-full text-base;
    font-size: 16px; /* Prevents zoom on iOS */
  }
  
  /* Card styles for mobile */
  .card-mobile {
    @apply card bg-base-100 shadow-sm border border-base-200;
  }
  
  /* Mobile navigation */
  .nav-mobile {
    @apply fixed bottom-0 left-0 right-0 bg-base-100 border-t border-base-200 safe-area-bottom;
    z-index: 1000;
  }
  
  /* Pull to refresh indicator */
  .pull-to-refresh {
    @apply flex items-center justify-center py-4;
    color: rgba(107, 114, 128, 0.6);
    transform: translateY(-100%);
    transition: transform 0.3s ease;
  }
  
  .pull-to-refresh.active {
    transform: translateY(0);
  }
  
  /* Loading states */
  .skeleton-mobile {
    @apply animate-pulse bg-base-200 rounded;
  }
  
  /* Mobile-specific spacing */
  .container-mobile {
    @apply container mx-auto px-4 max-w-mobile;
  }
  
  /* Status indicators */
  .status-active {
    @apply badge badge-success badge-sm;
  }
  
  .status-inactive {
    @apply badge badge-error badge-sm;
  }
  
  .status-pending {
    @apply badge badge-warning badge-sm;
  }
}

/* Utility classes */
@layer utilities {
  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* Touch manipulation */
  .touch-manipulation {
    touch-action: manipulation;
  }
  
  /* Prevent text selection on UI elements */
  .select-none-touch {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
  }
  
  /* Smooth scrolling */
  .scroll-smooth-mobile {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }
}

/* Animation classes */
@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-slide-in-up {
  animation: slideInUp 0.3s ease-out;
}

.animate-slide-in-down {
  animation: slideInDown 0.3s ease-out;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Dark mode support (if needed later) */
@media (prefers-color-scheme: dark) {
  /* Dark mode styles can be added here */
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card-mobile {
    @apply border-2;
  }
}
