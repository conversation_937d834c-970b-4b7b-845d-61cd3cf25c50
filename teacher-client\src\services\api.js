import axios from 'axios'
import { storageService } from './storage.js'

class ApiService {
    constructor() {
        this.client = null
        this.baseURL = ''
    }
    
    init(config) {
        this.baseURL = config.baseURL
        
        this.client = axios.create({
            baseURL: config.baseURL,
            timeout: config.timeout || 10000,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        
        // Request interceptor to add auth token
        this.client.interceptors.request.use(
            (config) => {
                const token = storageService.getToken()
                if (token) {
                    config.headers.Authorization = `Bearer ${token}`
                }
                return config
            },
            (error) => {
                return Promise.reject(error)
            }
        )
        
        console.log('API Service initialized with base URL:', config.baseURL)
    }
    
    setupAuthInterceptors(onUnauthorized) {
        // Response interceptor to handle auth errors
        this.client.interceptors.response.use(
            (response) => response,
            (error) => {
                if (error.response?.status === 401) {
                    console.log('Unauthorized request, logging out...')
                    onUnauthorized()
                }
                return Promise.reject(error)
            }
        )
    }
    
    // Generic request methods
    async get(url, config = {}) {
        try {
            const response = await this.client.get(url, config)
            return this.handleResponse(response)
        } catch (error) {
            throw this.handleError(error)
        }
    }
    
    async post(url, data = {}, config = {}) {
        try {
            const response = await this.client.post(url, data, config)
            return this.handleResponse(response)
        } catch (error) {
            throw this.handleError(error)
        }
    }
    
    async put(url, data = {}, config = {}) {
        try {
            const response = await this.client.put(url, data, config)
            return this.handleResponse(response)
        } catch (error) {
            throw this.handleError(error)
        }
    }
    
    async delete(url, config = {}) {
        try {
            const response = await this.client.delete(url, config)
            return this.handleResponse(response)
        } catch (error) {
            throw this.handleError(error)
        }
    }
    
    handleResponse(response) {
        return response.data
    }
    
    handleError(error) {
        console.error('API Error:', error)
        
        if (error.response) {
            // Server responded with error status
            const { status, data } = error.response
            return {
                status,
                message: data.message || data.error || 'An error occurred',
                errors: data.errors || {},
                data: data
            }
        } else if (error.request) {
            // Network error
            return {
                status: 0,
                message: 'Network error. Please check your connection.',
                errors: {},
                data: null
            }
        } else {
            // Other error
            return {
                status: 0,
                message: error.message || 'An unexpected error occurred',
                errors: {},
                data: null
            }
        }
    }
    
    // Specific API endpoints
    
    // Authentication endpoints
    async login(credentials) {
        return this.post('/auth/login', credentials)
    }
    
    async logout() {
        return this.post('/auth/logout')
    }
    
    async getCurrentUser() {
        return this.get('/auth/user')
    }
    
    async refreshToken() {
        return this.post('/auth/refresh')
    }
    
    // Teacher-specific endpoints
    async getTeacherPrograms() {
        return this.get('/teacher/programs')
    }
    
    async getProgramDetail(programId) {
        return this.get(`/teacher/programs/${programId}`)
    }
    
    async getProgramTeams(programId) {
        return this.get(`/teacher/programs/${programId}/teams`)
    }
    
    async getProgramBooks(programId) {
        return this.get(`/teacher/programs/${programId}/books`)
    }
    
    async getStudentProgress(programId, studentId) {
        return this.get(`/teacher/programs/${programId}/students/${studentId}/progress`)
    }
    
    // Active term information
    async getActiveTerm() {
        return this.get('/teacher/active-term')
    }
    
    // Team management
    async createTeam(programId, teamData) {
        return this.post(`/teacher/programs/${programId}/teams`, teamData)
    }
    
    async updateTeam(programId, teamId, teamData) {
        return this.put(`/teacher/programs/${programId}/teams/${teamId}`, teamData)
    }
    
    async deleteTeam(programId, teamId) {
        return this.delete(`/teacher/programs/${programId}/teams/${teamId}`)
    }
    
    async addTeamMember(programId, teamId, studentId) {
        return this.post(`/teacher/programs/${programId}/teams/${teamId}/members`, {
            student_id: studentId
        })
    }
    
    async removeTeamMember(programId, teamId, studentId) {
        return this.delete(`/teacher/programs/${programId}/teams/${teamId}/members/${studentId}`)
    }
}

export const apiService = new ApiService()
