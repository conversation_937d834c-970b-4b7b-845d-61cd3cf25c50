<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\SchoolClass;
use App\Models\Organization;
use App\Models\GradeLevel;
use App\Models\Role;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Fields\Text;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Laravel\Fields\Relationships\HasMany;
use MoonShine\UI\Fields\Number;
use MoonShine\Support\Attributes\Icon;


#[Icon('user-group')]

class SchoolClassResource extends BaseResource
{
    // TODO: Hide menu item if user is not system admin or group school admin/school admin
    // TODO: Group school admins/school admins can only see/create classes from their schools    

    protected string $model = SchoolClass::class;

    protected string $column = 'name';

    protected array $with = ['organization', 'gradeLevel', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.school_classes');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),            
            
            BelongsTo::make(__('admin.organization'), 'organization', 
                formatted: fn(Organization $org) => $org->name,
                resource: OrganizationResource::class)
                ->sortable(),
            
            BelongsTo::make(__('admin.level'), 'gradeLevel', 
                formatted: fn(GradeLevel $grade) => $grade->name,
                resource: GradeLevelResource::class)
                ->sortable(),
            Text::make(__('admin.name'), 'name')
                ->sortable(),
            
            Number::make(__('admin.student_count'), 'student_count'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([                
                Flex::make([
                    BelongsTo::make(__('admin.organization'), 'organization', 
                        formatted: fn(Organization $org) => $org->name,
                        resource: OrganizationResource::class)
                        ->required()
                        ->placeholder(__('admin.select_organization'))
                        ->valuesQuery(function ($query) {
                            return $query->where('active', true)->where('org_type', Organization::TYPE_SCHOOL);
                        }),
                    
                    BelongsTo::make(__('admin.level'), 'gradeLevel', 
                        formatted: fn(GradeLevel $grade) => $grade->name,
                        resource: GradeLevelResource::class)
                        ->required()
                        ->placeholder(__('admin.select_grade_level')),

                    Text::make(__('admin.name'), 'name')
                        ->required()
                        ->placeholder(__('admin.enter_name')),
                ]),
            ]),
            
            
        ];
    }

    protected function detailFields(): iterable
    {
        return [
                BelongsTo::make(__('admin.organization'), 'organization', 
                    formatted: fn(Organization $org) => $org->name,resource: OrganizationResource::class),
                
                BelongsTo::make(__('admin.level'), 'gradeLevel', 
                    formatted: fn(GradeLevel $grade) => $grade->name, resource: GradeLevelResource::class),
                
                Text::make(__('admin.name'), 'name'),
                
                Number::make(__('admin.student_count'), 'student_count'),
                HasMany::make(__('admin.term_users'), 'termUsers', 
                    resource: TermUserResource::class),
                ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'organization_id' => ['required', 'exists:organizations,id'],
            'grade_level_id' => ['required', 'exists:grade_levels,id'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function resolveQuery(): object
    {
        $query = parent::resolveQuery();
        
        $user = auth('moonshine')->user();
        if ($user instanceof \App\Models\User && !$user->isSystemAdmin()) {
            // Non-system admins can only see classes from their organizations
            // This would need to be implemented based on term_users relationships
            // For now, we'll show all classes
        }
        
        return $query;
    }

    protected function checkCreatePermission($user): bool
    {
        // System admins and school admins can create classes
        return $user->isSystemAdmin() ||
               $user->hasRoleLevel(Role::LEVEL_SCHOOL_ADMIN);
    }

    protected function checkUpdatePermission($user): bool
    {
        // System admins and school admins can update classes
        return $user->isSystemAdmin() ||
               $user->hasRoleLevel(Role::LEVEL_SCHOOL_ADMIN);
    }

    protected function checkDeletePermission($user): bool
    {
        // System admins and school admins can delete classes
        return $user->isSystemAdmin() ||
               $user->hasRoleLevel(Role::LEVEL_SCHOOL_ADMIN);
    }

    protected function getSearchFields(): array
    {
        return ['name'];
    }

    protected function getDefaultSort(): array
    {
        return ['organization_id' => 'asc', 'grade_level_id' => 'asc', 'name' => 'asc'];
    }
}
