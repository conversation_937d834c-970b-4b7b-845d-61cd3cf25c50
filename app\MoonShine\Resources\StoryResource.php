<?php

namespace App\MoonShine\Resources;

use App\Models\Story;
use App\Models\Role;
use MoonShine\Support\Attributes\Icon;
use MoonShine\UI\Fields\{Text, Textarea, Image, Number, Switcher};
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Components\Tabs;
use MoonShine\UI\Components\Tabs\Tab;


#[Icon('book-open')]
class StoryResource extends BaseResource
{
    protected string $model = Story::class;

    protected string $column = 'title';

    protected array $with = ['creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.stories');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            Text::make(__('admin.title'), 'title')
                ->sortable(),
            
            Text::make(__('admin.map_dimensions'), 'map_dimensions')
                ->sortable(),
            
            Number::make(__('admin.chapter_count'), 'chapter_count'),
            
            Number::make(__('admin.character_count'), 'character_count'),
            
            Number::make(__('admin.achievement_count'), 'achievement_count'),
            
            Switcher::make(__('admin.active'), 'active')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Tabs::make([
                    Tab::make(__('admin.main_information'), [
                        Flex::make([
                            Text::make(__('admin.title'), 'title')
                                ->required()
                                ->placeholder(__('admin.enter_title')),
                        ]),
                        
                        Textarea::make(__('admin.description'), 'description')
                            ->required()
                            ->placeholder(__('admin.enter_description')),
                        
                        Flex::make([
                            Image::make(__('admin.cover_image'), 'cover_image')
                                ->required(),
                            
                            Image::make(__('admin.map_background_image'), 'map_background_image')
                                ->nullable(),
                        ]),
                        
                        Flex::make([
                            Number::make(__('admin.map_grid_rows'), 'map_grid_rows')
                                ->required()
                                ->min(1)
                                ->max(50)
                                ->default(10),
                            
                            Number::make(__('admin.map_grid_columns'), 'map_grid_columns')
                                ->required()
                                ->min(1)
                                ->max(50)
                                ->default(10),
                        ]),
                        
                        Switcher::make(__('admin.active'), 'active')
                            ->default(true),
                    ]),
                    
                    Tab::make(__('admin.summary'), [
                        Text::make(__('admin.chapter_count'), 'chapter_count')
                            ->readonly(),
                        
                        Text::make(__('admin.character_count'), 'character_count')
                            ->readonly(),
                        
                        Text::make(__('admin.achievement_count'), 'achievement_count')
                            ->readonly(),
                        
                        Text::make(__('admin.map_dimensions'), 'map_dimensions')
                            ->readonly(),
                    ]),
                ]),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.title'), 'title'),
            Textarea::make(__('admin.description'), 'description'),
            Image::make(__('admin.cover_image'), 'cover_image'),
            Image::make(__('admin.map_background_image'), 'map_background_image'),
            Number::make(__('admin.map_grid_rows'), 'map_grid_rows'),
            Number::make(__('admin.map_grid_columns'), 'map_grid_columns'),
            Text::make(__('admin.map_dimensions'), 'map_dimensions'),
            Switcher::make(__('admin.active'), 'active'),
            Number::make(__('admin.chapter_count'), 'chapter_count'),
            Number::make(__('admin.character_count'), 'character_count'),
            Number::make(__('admin.achievement_count'), 'achievement_count'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string'],
            'cover_image' => ['required', 'string', 'max:255'],
            'map_background_image' => ['nullable', 'string', 'max:255'],
            'map_grid_rows' => ['required', 'integer', 'min:1', 'max:50'],
            'map_grid_columns' => ['required', 'integer', 'min:1', 'max:50'],
            'active' => ['boolean'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function checkCreatePermission($user): bool
    {
        // System admins, group school admins, and school admins can create stories
        return $user->isSystemAdmin() || 
               $user->hasRoleLevel(Role::LEVEL_GROUP_SCHOOL_ADMIN) ||
               $user->hasRoleLevel(Role::LEVEL_SCHOOL_ADMIN);
    }

    protected function checkUpdatePermission($user): bool
    {
        // System admins, group school admins, and school admins can update stories
        return $user->isSystemAdmin() || 
               $user->hasRoleLevel(Role::LEVEL_GROUP_SCHOOL_ADMIN) ||
               $user->hasRoleLevel(Role::LEVEL_SCHOOL_ADMIN);
    }

    protected function checkDeletePermission($user): bool
    {
        // Only system admins and group school admins can delete stories
        return $user->isSystemAdmin() || 
               $user->hasRoleLevel(Role::LEVEL_GROUP_SCHOOL_ADMIN);
    }

    protected function getSearchFields(): array
    {
        return ['title', 'description'];
    }

    protected function getDefaultSort(): array
    {
        return ['title' => 'asc'];
    }
}
