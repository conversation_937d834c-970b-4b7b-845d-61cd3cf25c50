import { apiService } from './api.js'
import { storageService } from './storage.js'

class AuthService {
    constructor() {
        this.user = null
        this.isAuthenticated = false
    }
    
    async login(credentials) {
        try {
            console.log('🎮 Attempting student login...')
            
            const response = await apiService.login({
                email: credentials.email,
                password: credentials.password
            })
            
            if (response.token && response.user) {
                // Verify user is a student (role level 4)
                if (response.user.role && response.user.role.level === 4) {
                    // Store token and user data
                    storageService.setToken(response.token)
                    storageService.setUser(response.user)
                    
                    this.user = response.user
                    this.isAuthenticated = true
                    
                    console.log('🎉 Student login successful:', response.user)
                    
                    return {
                        success: true,
                        user: response.user,
                        token: response.token
                    }
                } else {
                    console.log('❌ User is not a student')
                    return {
                        success: false,
                        message: 'This account is not for students. Please use the teacher portal.'
                    }
                }
            } else {
                console.error('❌ Invalid login response:', response)
                return {
                    success: false,
                    message: response.message || 'Invalid login response'
                }
            }
        } catch (error) {
            console.error('🚨 Login error:', error)
            
            if (error.status === 401) {
                return {
                    success: false,
                    message: 'Invalid email or password'
                }
            } else if (error.status === 422) {
                return {
                    success: false,
                    message: 'Please check your email and password',
                    errors: error.errors
                }
            } else if (error.status === 0) {
                return {
                    success: false,
                    message: 'Unable to connect to server. Please check your internet connection.'
                }
            } else {
                return {
                    success: false,
                    message: error.message || 'Login failed. Please try again.'
                }
            }
        }
    }
    
    async logout() {
        try {
            // Call logout endpoint if we have a token
            if (storageService.getToken()) {
                await apiService.logout()
            }
        } catch (error) {
            console.error('🚨 Logout API call failed:', error)
            // Continue with local logout even if API call fails
        } finally {
            // Clear local storage
            this.clearAuthData()
        }
    }
    
    clearAuthData() {
        storageService.clearToken()
        storageService.clearUser()
        storageService.clearGameData() // Clear game-specific data
        this.user = null
        this.isAuthenticated = false
        console.log('🧹 Auth data cleared')
    }
    
    async getCurrentUser() {
        try {
            // First check if we have a stored user
            const storedUser = storageService.getUser()
            if (storedUser && storedUser.role && storedUser.role.level === 4) {
                this.user = storedUser
                this.isAuthenticated = true
                return storedUser
            }
            
            // If no stored user but we have a token, fetch from API
            const token = storageService.getToken()
            if (token) {
                const response = await apiService.getCurrentUser()
                if (response.user && response.user.role && response.user.role.level === 4) {
                    storageService.setUser(response.user)
                    this.user = response.user
                    this.isAuthenticated = true
                    return response.user
                }
            }
            
            return null
        } catch (error) {
            console.error('🚨 Get current user error:', error)
            this.clearAuthData()
            return null
        }
    }
    
    async refreshToken() {
        try {
            const response = await apiService.refreshToken()
            if (response.token) {
                storageService.setToken(response.token)
                return response.token
            }
            return null
        } catch (error) {
            console.error('🚨 Token refresh failed:', error)
            this.clearAuthData()
            return null
        }
    }
    
    isLoggedIn() {
        return this.isAuthenticated && !!storageService.getToken()
    }
    
    getUser() {
        return this.user || storageService.getUser()
    }
    
    getToken() {
        return storageService.getToken()
    }
    
    // Check if user is a student
    isStudent() {
        const user = this.getUser()
        return user && user.role && user.role.level === 4 // Student role level
    }
    
    // Get user's display name
    getUserDisplayName() {
        const user = this.getUser()
        if (!user) return 'Student'
        
        if (user.title && user.name) {
            return `${user.title} ${user.name}`
        }
        return user.name || user.email || 'Student'
    }
    
    // Get user's first name for casual display
    getUserFirstName() {
        const user = this.getUser()
        if (!user || !user.name) return 'Student'
        
        return user.name.split(' ')[0]
    }
    
    // Get user's grade level or class info
    getUserGradeInfo() {
        const user = this.getUser()
        // This would come from the user's class assignment
        // For now, return a placeholder
        return user?.grade || 'Student'
    }
    
    // Get user's school information
    getUserSchool() {
        const user = this.getUser()
        return user?.school?.name || 'School'
    }
    
    // Validate token expiration (if JWT contains exp claim)
    isTokenExpired() {
        const token = this.getToken()
        if (!token) return true
        
        try {
            // Decode JWT payload (basic check, not cryptographically verified)
            const payload = JSON.parse(atob(token.split('.')[1]))
            const currentTime = Math.floor(Date.now() / 1000)
            return payload.exp < currentTime
        } catch (error) {
            console.error('🚨 Token validation error:', error)
            return true
        }
    }
    
    // Get user avatar color based on name
    getUserAvatarColor() {
        const colors = [
            'bg-red-400', 'bg-blue-400', 'bg-green-400', 
            'bg-yellow-400', 'bg-purple-400', 'bg-pink-400',
            'bg-indigo-400', 'bg-orange-400', 'bg-teal-400'
        ]
        const name = this.getUserDisplayName()
        const colorIndex = name.charCodeAt(0) % colors.length
        return colors[colorIndex]
    }
    
    // Get user initials for avatar
    getUserInitials() {
        const name = this.getUserDisplayName()
        const parts = name.split(' ')
        if (parts.length >= 2) {
            return (parts[0].charAt(0) + parts[1].charAt(0)).toUpperCase()
        }
        return name.charAt(0).toUpperCase()
    }
}

export const authService = new AuthService()
