import Alpine from 'alpinejs'
import { apiService } from '../../services/api.js'
import { gameService } from '../../services/game.js'

Alpine.data('dashboard', () => ({
    // Dashboard data
    programs: [],
    gameData: {},
    achievements: [],
    recentActivity: [],
    
    // Component state
    isLoading: true,
    error: null,
    lastUpdated: null,
    
    // Animation state
    showWelcomeAnimation: false,
    
    async init() {
        console.log('🏠 Dashboard component initialized')
        
        // Show welcome animation for new sessions
        this.checkWelcomeAnimation()
        
        // Load dashboard data
        await this.loadDashboardData()
        
        // Listen for refresh events
        this.$watch('$store.app.currentRoute', (newRoute) => {
            if (newRoute === 'dashboard') {
                this.refreshData()
            }
        })
        
        // Listen for game events
        window.addEventListener('game-celebration', () => {
            this.refreshGameData()
        })
    },
    
    checkWelcomeAnimation() {
        const lastLogin = localStorage.getItem('reading_quest_last_dashboard_visit')
        const today = new Date().toDateString()
        
        if (lastLogin !== today) {
            this.showWelcomeAnimation = true
            localStorage.setItem('reading_quest_last_dashboard_visit', today)
            
            // Hide animation after 3 seconds
            setTimeout(() => {
                this.showWelcomeAnimation = false
            }, 3000)
        }
    },
    
    async loadDashboardData() {
        this.isLoading = true
        this.error = null
        
        try {
            // Load data in parallel
            const [programsResponse, gameDataResponse] = await Promise.all([
                this.loadPrograms(),
                this.loadGameData()
            ])
            
            // Load achievements
            await this.loadAchievements()
            
            // Generate recent activity
            this.generateRecentActivity()
            
            this.lastUpdated = new Date()
            
            console.log('🎮 Dashboard data loaded successfully')
        } catch (error) {
            console.error('🚨 Failed to load dashboard data:', error)
            this.error = 'Failed to load dashboard data. Please try again.'
        } finally {
            this.isLoading = false
        }
    },
    
    async loadPrograms() {
        try {
            const response = await apiService.getStudentPrograms()
            this.programs = response.programs || []
            return response
        } catch (error) {
            console.error('🚨 Failed to load programs:', error)

            // Check if we're in demo mode or if API is not available
            const urlParams = new URLSearchParams(window.location.search)
            const hashContainsDemo = window.location.hash.includes('demo')
            const isDemoMode = urlParams.has('demo') || hashContainsDemo || import.meta.env.VITE_DEMO_MODE === 'true'

            if (isDemoMode || error.status === 0) {
                console.log('🎭 Loading demo data for student programs')
                this.programs = this.getDemoPrograms()
                return { programs: this.programs }
            }

            this.programs = []
            throw error
        }
    },
    
    async loadGameData() {
        try {
            this.gameData = await gameService.getUserGameData()
            return this.gameData
        } catch (error) {
            console.error('🚨 Failed to load game data:', error)

            // Provide demo game data
            const urlParams = new URLSearchParams(window.location.search)
            const hashContainsDemo = window.location.hash.includes('demo')
            const isDemoMode = urlParams.has('demo') || hashContainsDemo || import.meta.env.VITE_DEMO_MODE === 'true'

            if (isDemoMode || error.status === 0) {
                this.gameData = this.getDemoGameData()
            } else {
                this.gameData = gameService.getGameData()
            }
        }
    },
    
    async loadAchievements() {
        try {
            const response = await apiService.getStudentAchievements()
            this.achievements = response.achievements || []
            
            // Also get local achievements
            const localAchievements = gameService.getAchievements()
            
            // Merge and deduplicate
            const allAchievements = [...this.achievements, ...localAchievements]
            this.achievements = allAchievements.filter((achievement, index, self) => 
                index === self.findIndex(a => a.id === achievement.id)
            )
        } catch (error) {
            console.error('🚨 Failed to load achievements:', error)
            this.achievements = gameService.getAchievements()
        }
    },
    
    generateRecentActivity() {
        // Generate recent activity based on game data and programs
        this.recentActivity = []
        
        // Add recent achievements
        const recentAchievements = this.achievements
            .filter(a => a.unlockedAt)
            .sort((a, b) => new Date(b.unlockedAt) - new Date(a.unlockedAt))
            .slice(0, 3)
        
        recentAchievements.forEach(achievement => {
            this.recentActivity.push({
                type: 'achievement',
                title: `Unlocked "${achievement.name}"`,
                description: achievement.description,
                icon: '🏆',
                time: this.formatRelativeTime(achievement.unlockedAt),
                color: 'text-yellow-600'
            })
        })
        
        // Add program progress
        this.programs.forEach(program => {
            if (program.progress && program.progress > 0) {
                this.recentActivity.push({
                    type: 'progress',
                    title: `Progress in ${program.name}`,
                    description: `${program.progress}% complete`,
                    icon: '📈',
                    time: 'Recently',
                    color: 'text-blue-600'
                })
            }
        })
        
        // Sort by time and limit to 5 items
        this.recentActivity = this.recentActivity
            .sort((a, b) => new Date(b.time) - new Date(a.time))
            .slice(0, 5)
    },
    
    async refreshData() {
        await this.loadDashboardData()
    },
    
    async refreshGameData() {
        await this.loadGameData()
        await this.loadAchievements()
        this.generateRecentActivity()
    },
    
    // Navigation methods
    viewProgram(programId) {
        this.$store.app.navigateTo('program', { id: programId })
    },
    
    goToLibrary() {
        this.$store.app.navigateTo('library')
    },
    
    goToAchievements() {
        this.$store.app.navigateTo('achievements')
    },
    
    goToProfile() {
        this.$store.app.navigateTo('profile')
    },
    
    // Utility methods
    formatDate(date) {
        if (!date) return 'N/A'
        return new Date(date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        })
    },
    
    formatRelativeTime(date) {
        if (!date) return 'Recently'
        
        const now = new Date()
        const past = new Date(date)
        const diffMs = now - past
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
        
        if (diffDays === 0) return 'Today'
        if (diffDays === 1) return 'Yesterday'
        if (diffDays < 7) return `${diffDays} days ago`
        return this.formatDate(date)
    },
    
    getGreeting() {
        const hour = new Date().getHours()
        const name = this.$store.app.getUserDisplayName().split(' ')[0]
        
        if (hour < 12) return `Good morning, ${name}! ☀️`
        if (hour < 17) return `Good afternoon, ${name}! 🌤️`
        return `Good evening, ${name}! 🌙`
    },
    
    getMotivationalMessage() {
        const messages = [
            "Ready for today's reading adventure? 🚀",
            "Every page you read makes you stronger! 💪",
            "Your reading journey is amazing! ✨",
            "Time to unlock new worlds through books! 🌟",
            "You're becoming a reading champion! 🏆"
        ]
        return messages[Math.floor(Math.random() * messages.length)]
    },
    
    // Game data helpers
    getUserLevel() {
        return this.gameData.level || 1
    },
    
    getUserPoints() {
        return this.gameData.points || 0
    },
    
    getUserStreak() {
        return this.gameData.streak || 0
    },
    
    getBooksRead() {
        return this.gameData.booksRead || 0
    },
    
    getProgressToNextLevel() {
        return gameService.getProgressToNextLevel()
    },
    
    // Program helpers
    getActivePrograms() {
        return this.programs.filter(program => program.active)
    },
    
    hasPrograms() {
        return this.programs.length > 0
    },
    
    hasActivePrograms() {
        return this.getActivePrograms().length > 0
    },
    
    // Achievement helpers
    getRecentAchievements() {
        return this.achievements
            .filter(a => a.unlockedAt)
            .sort((a, b) => new Date(b.unlockedAt) - new Date(a.unlockedAt))
            .slice(0, 3)
    },
    
    hasRecentAchievements() {
        return this.getRecentAchievements().length > 0
    },
    
    // Activity helpers
    hasRecentActivity() {
        return this.recentActivity.length > 0
    },
    
    // Quick actions
    async startQuickReading() {
        // Find the first available book to read
        if (this.hasActivePrograms()) {
            const firstProgram = this.getActivePrograms()[0]
            this.viewProgram(firstProgram.id)
        } else {
            this.goToLibrary()
        }
    },
    
    celebrateStreak() {
        if (this.getUserStreak() > 0) {
            this.$store.app.showGameToast(`🔥 ${this.getUserStreak()} day streak! Keep it up!`, 'success')
        }
    },

    // Demo data for testing
    getDemoPrograms() {
        return [
            {
                id: 1,
                name: 'Summer Reading Adventure',
                description: 'Join us on an exciting summer reading journey!',
                active: true,
                progress: 75,
                booksCompleted: 3,
                totalBooks: 4,
                color: 'bg-gradient-to-r from-purple-400 to-pink-400'
            },
            {
                id: 2,
                name: 'Mystery Solvers Club',
                description: 'Solve mysteries and puzzles through reading!',
                active: true,
                progress: 40,
                booksCompleted: 2,
                totalBooks: 5,
                color: 'bg-gradient-to-r from-blue-400 to-cyan-400'
            },
            {
                id: 3,
                name: 'Fantasy Realm Explorer',
                description: 'Explore magical worlds and mythical creatures!',
                active: false,
                progress: 100,
                booksCompleted: 6,
                totalBooks: 6,
                color: 'bg-gradient-to-r from-green-400 to-emerald-400'
            }
        ]
    },

    getDemoGameData() {
        return {
            level: 5,
            points: 1250,
            streak: 7,
            booksRead: 11,
            totalReadingTime: 2340, // minutes
            achievements: 8,
            character: {
                name: 'Reading Wizard',
                avatar: '🧙‍♂️',
                experience: 1250
            }
        }
    }
}))

// Export template for reference
export const dashboardTemplate = `
<!-- Dashboard template will be embedded in index.html -->
`
