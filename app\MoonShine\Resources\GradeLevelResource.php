<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\GradeLevel;
use App\Models\Role;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Fields\Text;


use MoonShine\Support\Attributes\Icon;
use MoonShine\Contracts\UI\FieldContract;
use MoonShine\Contracts\UI\ComponentContract;
use MoonShine\UI\Fields\Date;


#[Icon('academic-cap')]

class GradeLevelResource extends BaseResource
{
    protected string $model = GradeLevel::class;

    protected string $column = 'name';

    protected array $with = ['creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.grade_levels');
    }

    public function indexFields(): array
    {
        return [
            ...parent::getCommonIndexFields(),
            
            Text::make(__('admin.name'), 'name')
                ->sortable(),
            
            Text::make(__('admin.formatted_name'), 'formatted_name'),
        ];
    }

    public function formFields(): array
    {
        return [
            Box::make([
                Text::make(__('admin.name'), 'name')
                    ->required()
                    ->placeholder(__('admin.enter_name')),
            ]),
            
            
        ];
    }

    public function detailFields(): array
    {
        return [
            ID::make(),
            Text::make(__('admin.name'), 'name'),
            Text::make(__('admin.formatted_name'), 'formatted_name'),
            Date::make(__('admin.created_at'), 'created_at')
                ->format('d.m.Y H:i'),
            Date::make(__('admin.updated_at'), 'updated_at')
                ->format('d.m.Y H:i'),
            Text::make(__('admin.created_by'), 'creator.name'),
            Text::make(__('admin.updated_by'), 'updater.name'),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'name' => ['required', 'string', 'max:255', 'unique:grade_levels,name,' . $item?->id],
            ...parent::getCommonRules($item),
        ];
    }

    protected function checkCreatePermission($user): bool
    {
        // System admins and group school admins can create grade levels
        return $user->isSystemAdmin() || $user->hasRoleLevel(Role::LEVEL_GROUP_SCHOOL_ADMIN);
    }

    protected function checkUpdatePermission($user): bool
    {
        // System admins and group school admins can update grade levels
        return $user->isSystemAdmin() || $user->hasRoleLevel(Role::LEVEL_GROUP_SCHOOL_ADMIN);
    }

    protected function checkDeletePermission($user): bool
    {
        // Only system admins can delete grade levels
        return $user->isSystemAdmin();
    }

    protected function getSearchFields(): array
    {
        return ['name'];
    }

    protected function getDefaultSort(): array
    {
        return ['name' => 'asc'];
    }
}
