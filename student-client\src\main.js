import Alpine from 'alpinejs'
import { apiService } from './services/api.js'
import { authService } from './services/auth.js'
import { gameService } from './services/game.js'
import { router } from './services/router.js'
import { storageService } from './services/storage.js'
import './styles/main.css'

// Import Alpine.js components
import './components/auth/LoginForm.js'
import './components/dashboard/Dashboard.js'
import './components/dashboard/ProgramCard.js'
import './components/layout/AppLayout.js'
import './components/layout/BottomNav.js'
import './components/program/ProgramDetail.js'

// Global Alpine.js data and methods
Alpine.data('app', () => ({
    // App state
    isLoading: true,
    isAuthenticated: false,
    user: null,
    currentRoute: 'login',
    userPoints: 0,
    userLevel: 1,
    
    // Initialize app
    async init() {
        console.log('🎮 Initializing Reading Quest...')
        
        // Hide initial loader
        const loader = document.getElementById('initial-loader')
        if (loader) {
            loader.style.display = 'none'
        }
        
        // Initialize services
        await this.initializeServices()
        
        // Check authentication status
        await this.checkAuth()
        
        // Initialize router
        this.initializeRouter()
        
        // Load user game data if authenticated
        if (this.isAuthenticated) {
            await this.loadUserGameData()
        }
        
        this.isLoading = false
        
        console.log('🎉 Reading Quest initialized successfully!')
        console.log('🔍 Current state:', {
            isAuthenticated: this.isAuthenticated,
            currentRoute: this.currentRoute,
            user: this.user,
            userPoints: this.userPoints,
            userLevel: this.userLevel
        })
    },
    
    async initializeServices() {
        // Initialize API service with base URL
        apiService.init({
            baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
            timeout: 10000
        })
        
        // Set up API interceptors for auth
        apiService.setupAuthInterceptors(() => {
            this.logout()
        })
        
        // Initialize game service
        gameService.init()
    },
    
    async checkAuth() {
        // Demo mode for testing - check if URL contains demo parameter or hash
        const urlParams = new URLSearchParams(window.location.search)
        const hashContainsDemo = window.location.hash.includes('demo')
        const isDemoMode = urlParams.has('demo') || hashContainsDemo || import.meta.env.VITE_DEMO_MODE === 'true'

        if (isDemoMode) {
            console.log('🎭 Running in demo mode - Student Portal')
            this.isAuthenticated = true
            this.user = {
                id: 1,
                name: 'Demo Student',
                email: '<EMAIL>',
                role: {
                    name: 'Student',
                    level: 4
                }
            }
            this.userPoints = 1250
            this.userLevel = 5
            this.currentRoute = 'dashboard'
            return
        }

        const token = storageService.getToken()
        if (token) {
            try {
                const user = await authService.getCurrentUser()
                if (user && user.role && user.role.level === 4) { // Student role level
                    this.isAuthenticated = true
                    this.user = user
                    this.currentRoute = 'dashboard'
                    return
                }
            } catch (error) {
                console.error('Auth check failed:', error)
                storageService.clearToken()
            }
        }

        this.isAuthenticated = false
        this.user = null
        this.currentRoute = 'login'
    },
    
    async loadUserGameData() {
        try {
            const gameData = await gameService.getUserGameData()
            this.userPoints = gameData.points || 0
            this.userLevel = gameData.level || 1
        } catch (error) {
            console.error('Failed to load game data:', error)
        }
    },
    
    initializeRouter() {
        router.init((route) => {
            this.currentRoute = route
        })
    },
    
    async login(credentials) {
        try {
            const response = await authService.login(credentials)
            if (response.success) {
                // Verify user is a student
                if (response.user.role && response.user.role.level === 4) {
                    this.isAuthenticated = true
                    this.user = response.user
                    this.currentRoute = 'dashboard'
                    
                    // Load game data
                    await this.loadUserGameData()
                    
                    // Show welcome message
                    this.showGameToast(`Welcome back, ${response.user.name}! 🎉`, 'success')
                    
                    return { success: true }
                } else {
                    // Not a student account
                    authService.logout()
                    return { success: false, message: 'This account is not for students. Please use the teacher portal.' }
                }
            }
            return { success: false, message: response.message }
        } catch (error) {
            console.error('Login error:', error)
            return { success: false, message: 'Login failed. Please try again.' }
        }
    },
    
    logout() {
        authService.logout()
        this.isAuthenticated = false
        this.user = null
        this.userPoints = 0
        this.userLevel = 1
        this.currentRoute = 'login'
        router.navigate('login')
        this.showGameToast('See you next time! 👋', 'info')
    },
    
    // Navigation methods
    navigateTo(route) {
        router.navigate(route)
    },
    
    // Game-specific methods
    addPoints(points) {
        this.userPoints += points
        gameService.updatePoints(this.userPoints)
        this.showGameToast(`+${points} points! 🌟`, 'success')
    },
    
    levelUp() {
        this.userLevel += 1
        gameService.updateLevel(this.userLevel)
        this.showGameToast(`Level Up! You're now level ${this.userLevel}! 🎊`, 'success')
    },
    
    // Utility methods
    formatDate(date) {
        if (!date) return ''
        return new Date(date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        })
    },
    
    showGameToast(message, type = 'info') {
        // Enhanced toast with game-style animations
        const toast = document.createElement('div')
        toast.className = `toast toast-top toast-center z-50`
        
        const alertClass = {
            success: 'alert-success',
            error: 'alert-error',
            warning: 'alert-warning',
            info: 'alert-info'
        }[type] || 'alert-info'
        
        const icon = {
            success: '🎉',
            error: '😞',
            warning: '⚠️',
            info: 'ℹ️'
        }[type] || 'ℹ️'
        
        toast.innerHTML = `
            <div class="alert ${alertClass} shadow-lg animate-bounce">
                <div class="flex items-center gap-2">
                    <span class="text-lg">${icon}</span>
                    <span class="font-semibold">${message}</span>
                </div>
            </div>
        `
        document.body.appendChild(toast)
        
        // Add sparkle effect for success messages
        if (type === 'success') {
            this.addSparkleEffect(toast)
        }
        
        setTimeout(() => {
            toast.remove()
        }, 4000)
    },
    
    addSparkleEffect(element) {
        // Add sparkle animation around the element
        for (let i = 0; i < 5; i++) {
            setTimeout(() => {
                const sparkle = document.createElement('div')
                sparkle.className = 'absolute w-2 h-2 bg-yellow-400 rounded-full animate-ping'
                sparkle.style.left = Math.random() * 100 + '%'
                sparkle.style.top = Math.random() * 100 + '%'
                element.appendChild(sparkle)
                
                setTimeout(() => sparkle.remove(), 1000)
            }, i * 100)
        }
    },
    
    // Get user display info
    getUserDisplayName() {
        return this.user?.name || 'Student'
    },
    
    getUserAvatar() {
        // Generate a colorful avatar based on user name
        const colors = ['bg-red-400', 'bg-blue-400', 'bg-green-400', 'bg-yellow-400', 'bg-purple-400', 'bg-pink-400']
        const name = this.getUserDisplayName()
        const colorIndex = name.charCodeAt(0) % colors.length
        return colors[colorIndex]
    },
    
    // Progress calculations
    getProgressPercentage(current, total) {
        if (!total || total === 0) return 0
        return Math.min(Math.round((current / total) * 100), 100)
    },
    
    // Achievement helpers
    checkAchievements(action, data = {}) {
        gameService.checkAchievements(action, data)
    }
}))

// Start Alpine.js
Alpine.start()

// Initialize app when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 DOM loaded, starting Reading Quest initialization...')
})
