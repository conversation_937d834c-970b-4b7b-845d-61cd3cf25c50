import Alpine from 'alpinejs'
import { apiService } from '../../services/api.js'

Alpine.data('programDetail', () => ({
    // Program data
    program: null,
    teams: [],
    books: [],
    
    // Component state
    isLoading: true,
    error: null,
    activeTab: 'overview',
    
    // Get program ID from route
    get programId() {
        return this.$store.router?.getRouteParams()?.id
    },
    
    async init() {
        console.log('Program detail component initialized')
        
        if (!this.programId) {
            this.error = 'Program ID not found'
            this.isLoading = false
            return
        }
        
        await this.loadProgramData()
    },
    
    async loadProgramData() {
        this.isLoading = true
        this.error = null
        
        try {
            // Load program details
            const programResponse = await apiService.getProgramDetail(this.programId)
            this.program = programResponse.program
            
            // Load teams and books in parallel
            const [teamsResponse, booksResponse] = await Promise.all([
                apiService.getProgramTeams(this.programId),
                apiService.getProgramBooks(this.programId)
            ])
            
            this.teams = teamsResponse.teams || []
            this.books = booksResponse.books || []
            
            console.log('Program data loaded successfully')
        } catch (error) {
            console.error('Failed to load program data:', error)
            this.error = 'Failed to load program details. Please try again.'
            this.$store.app.showToast('Failed to load program details', 'error')
        } finally {
            this.isLoading = false
        }
    },
    
    // Tab management
    setActiveTab(tab) {
        this.activeTab = tab
    },
    
    isActiveTab(tab) {
        return this.activeTab === tab
    },
    
    // Navigation
    goBack() {
        this.$store.app.navigateTo('dashboard')
    },
    
    // Utility methods
    formatDate(date) {
        if (!date) return 'N/A'
        return new Date(date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        })
    },
    
    getStatusBadge() {
        if (!this.program) return 'badge-ghost'
        return this.program.active ? 'badge-success' : 'badge-error'
    },
    
    getStatusText() {
        if (!this.program) return 'Unknown'
        return this.program.active ? 'Active' : 'Inactive'
    },
    
    getProgressPercentage() {
        if (!this.program || !this.program.total_books || this.program.total_books === 0) {
            return 0
        }
        return Math.round((this.program.completed_books / this.program.total_books) * 100)
    },
    
    // Team methods
    hasTeams() {
        return this.teams.length > 0
    },
    
    getTeamMemberCount(team) {
        return team.members?.length || 0
    },
    
    // Book methods
    hasBooks() {
        return this.books.length > 0
    },
    
    getBookProgress(book) {
        if (!book.total_readers || book.total_readers === 0) return 0
        return Math.round((book.completed_readers / book.total_readers) * 100)
    }
}))

// Export template for reference (template is embedded in index.html)
export const programDetailTemplate = `
<!-- Program detail template is embedded in index.html -->


`
