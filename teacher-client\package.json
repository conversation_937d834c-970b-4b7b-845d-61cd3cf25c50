{"name": "teacher-client", "version": "1.0.0", "description": "Mobile-first teacher interface for gamified reading tracker", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "vite preview --port 3000", "setup": "node scripts/dev-setup.js"}, "keywords": ["education", "reading", "teacher", "mobile", "spa"], "author": "", "license": "MIT", "devDependencies": {"vite": "^5.0.0", "@vitejs/plugin-legacy": "^5.0.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6"}, "dependencies": {"alpinejs": "^3.13.3", "daisyui": "^4.4.19", "axios": "^1.6.2"}}