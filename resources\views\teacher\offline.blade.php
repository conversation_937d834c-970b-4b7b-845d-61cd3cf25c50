@extends('layouts.teacher')

@section('title', __('teacher.offline_title', [], 'Offline - Teacher App'))

@section('content')
<div class="min-h-screen flex items-center justify-center px-4 py-12">
    <div class="max-w-md w-full text-center">
        <!-- Offline Icon -->
        <div class="mx-auto h-24 w-24 bg-gray-100 rounded-full flex items-center justify-center mb-8">
            <svg class="h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 11-9.75 9.75 9.75 9.75 0 019.75-9.75z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6l4 2"></path>
            </svg>
        </div>

        <!-- Offline Message -->
        <h1 class="text-2xl font-bold text-gray-900 mb-4">
            {{ __('teacher.offline_title', [], 'You\'re Offline') }}
        </h1>
        
        <p class="text-gray-600 mb-8">
            {{ __('teacher.offline_message', [], 'It looks like you\'re not connected to the internet. Some features may not be available until you reconnect.') }}
        </p>

        <!-- Cached Content Notice -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-3 text-left">
                    <h3 class="text-sm font-medium text-blue-800">
                        {{ __('teacher.offline_cached_title', [], 'Limited Functionality') }}
                    </h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <p>{{ __('teacher.offline_cached_message', [], 'You can still access previously loaded content, but new data won\'t be available until you reconnect.') }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Available Actions -->
        <div class="space-y-4">
            <button 
                onclick="checkConnection()" 
                class="btn-touch btn-primary w-full flex justify-center items-center"
                id="retry-button"
            >
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                <span id="retry-text">{{ __('teacher.retry_connection', [], 'Try Again') }}</span>
            </button>

            <a 
                href="{{ route('teacher.dashboard') }}" 
                class="btn-touch bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200 w-full flex justify-center items-center"
            >
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                </svg>
                {{ __('teacher.go_to_dashboard', [], 'Go to Dashboard') }}
            </a>
        </div>

        <!-- Connection Status -->
        <div class="mt-8 p-4 bg-gray-50 rounded-lg">
            <div class="flex items-center justify-center">
                <div class="flex items-center">
                    <div id="connection-indicator" class="h-3 w-3 bg-red-500 rounded-full mr-2"></div>
                    <span id="connection-status" class="text-sm text-gray-600">
                        {{ __('teacher.connection_offline', [], 'Offline') }}
                    </span>
                </div>
            </div>
        </div>

        <!-- Tips -->
        <div class="mt-8 text-left">
            <h3 class="text-sm font-medium text-gray-900 mb-3">
                {{ __('teacher.offline_tips_title', [], 'Tips while offline:') }}
            </h3>
            <ul class="text-sm text-gray-600 space-y-2">
                <li class="flex items-start">
                    <span class="text-purple-500 mr-2">•</span>
                    {{ __('teacher.offline_tip_1', [], 'Previously loaded pages will still work') }}
                </li>
                <li class="flex items-start">
                    <span class="text-purple-500 mr-2">•</span>
                    {{ __('teacher.offline_tip_2', [], 'Your data will sync when you reconnect') }}
                </li>
                <li class="flex items-start">
                    <span class="text-purple-500 mr-2">•</span>
                    {{ __('teacher.offline_tip_3', [], 'Check your WiFi or mobile data connection') }}
                </li>
            </ul>
        </div>
    </div>
</div>

@push('scripts')
<script>
    let isOnline = navigator.onLine;
    
    function updateConnectionStatus() {
        const indicator = document.getElementById('connection-indicator');
        const status = document.getElementById('connection-status');
        
        if (navigator.onLine) {
            indicator.className = 'h-3 w-3 bg-green-500 rounded-full mr-2';
            status.textContent = '{{ __('teacher.connection_online', [], 'Online') }}';
            
            // Auto-redirect to dashboard when back online
            setTimeout(() => {
                window.location.href = '{{ route('teacher.dashboard') }}';
            }, 1000);
        } else {
            indicator.className = 'h-3 w-3 bg-red-500 rounded-full mr-2';
            status.textContent = '{{ __('teacher.connection_offline', [], 'Offline') }}';
        }
    }
    
    function checkConnection() {
        const button = document.getElementById('retry-button');
        const text = document.getElementById('retry-text');
        
        button.disabled = true;
        text.textContent = '{{ __('teacher.checking_connection', [], 'Checking...') }}';
        
        // Try to fetch a small resource to test connection
        fetch('/teacher-manifest.json', { 
            method: 'HEAD',
            cache: 'no-cache'
        })
        .then(response => {
            if (response.ok) {
                text.textContent = '{{ __('teacher.connection_restored', [], 'Connected!') }}';
                updateConnectionStatus();
                
                // Redirect to dashboard
                setTimeout(() => {
                    window.location.href = '{{ route('teacher.dashboard') }}';
                }, 1000);
            } else {
                throw new Error('Connection failed');
            }
        })
        .catch(error => {
            text.textContent = '{{ __('teacher.still_offline', [], 'Still offline') }}';
            setTimeout(() => {
                text.textContent = '{{ __('teacher.retry_connection', [], 'Try Again') }}';
                button.disabled = false;
            }, 2000);
        });
    }
    
    // Listen for online/offline events
    window.addEventListener('online', updateConnectionStatus);
    window.addEventListener('offline', updateConnectionStatus);
    
    // Initial status check
    document.addEventListener('DOMContentLoaded', updateConnectionStatus);
    
    // Periodic connection check
    setInterval(() => {
        if (!navigator.onLine) {
            // Try a lightweight request to check actual connectivity
            fetch('/teacher-manifest.json', { 
                method: 'HEAD',
                cache: 'no-cache'
            })
            .then(response => {
                if (response.ok && !isOnline) {
                    // Connection restored
                    isOnline = true;
                    updateConnectionStatus();
                }
            })
            .catch(() => {
                isOnline = false;
            });
        }
    }, 10000); // Check every 10 seconds
</script>
@endpush
@endsection
