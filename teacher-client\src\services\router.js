class Router {
    constructor() {
        this.routes = new Map()
        this.currentRoute = ''
        this.onRouteChange = null
        this.history = []
    }
    
    init(onRouteChange) {
        this.onRouteChange = onRouteChange
        
        // Define routes
        this.defineRoutes()
        
        // Handle browser back/forward
        window.addEventListener('popstate', (event) => {
            const route = event.state?.route || this.getRouteFromHash()
            this.handleRouteChange(route, false)
        })
        
        // Handle initial route
        const initialRoute = this.getRouteFromHash() || 'login'
        this.handleRouteChange(initialRoute, true)
        
        console.log('Router initialized')
    }
    
    defineRoutes() {
        this.routes.set('login', {
            path: '#/login',
            title: 'Login',
            requiresAuth: false
        })
        
        this.routes.set('dashboard', {
            path: '#/dashboard',
            title: 'Dashboard',
            requiresAuth: true
        })
        
        this.routes.set('program', {
            path: '#/program/:id',
            title: 'Program Details',
            requiresAuth: true
        })
        
        this.routes.set('profile', {
            path: '#/profile',
            title: 'Profile',
            requiresAuth: true
        })
        
        this.routes.set('settings', {
            path: '#/settings',
            title: 'Settings',
            requiresAuth: true
        })
    }
    
    navigate(route, params = {}, addToHistory = true) {
        if (!this.routes.has(route)) {
            console.error(`Route '${route}' not found`)
            return
        }
        
        const routeConfig = this.routes.get(route)
        let path = routeConfig.path
        
        // Replace parameters in path
        Object.keys(params).forEach(key => {
            path = path.replace(`:${key}`, params[key])
        })
        
        // Update URL hash
        window.location.hash = path
        
        // Handle route change
        this.handleRouteChange(route, addToHistory, params)
    }
    
    handleRouteChange(route, addToHistory = true, params = {}) {
        const previousRoute = this.currentRoute
        this.currentRoute = route
        
        // Add to history
        if (addToHistory && route !== previousRoute) {
            this.history.push({
                route: previousRoute,
                timestamp: Date.now()
            })
            
            // Limit history size
            if (this.history.length > 50) {
                this.history = this.history.slice(-50)
            }
        }
        
        // Update browser history
        if (addToHistory) {
            const routeConfig = this.routes.get(route)
            if (routeConfig) {
                window.history.pushState(
                    { route, params },
                    routeConfig.title,
                    routeConfig.path
                )
                document.title = `${routeConfig.title} - Teacher App`
            }
        }
        
        // Notify route change
        if (this.onRouteChange) {
            this.onRouteChange(route, params)
        }
        
        console.log(`Route changed: ${previousRoute} -> ${route}`)
    }
    
    getRouteFromHash() {
        const hash = window.location.hash.slice(1) // Remove #
        if (!hash || hash === '/') return 'dashboard'
        
        // Extract route name from hash
        const pathParts = hash.split('/')
        const routeName = pathParts[1] || 'dashboard'
        
        return routeName
    }
    
    getRouteParams() {
        const hash = window.location.hash.slice(1)
        const pathParts = hash.split('/')
        const params = {}
        
        // Find matching route and extract parameters
        for (const [routeName, routeConfig] of this.routes) {
            const routeParts = routeConfig.path.slice(2).split('/') // Remove #/
            
            if (routeParts.length === pathParts.length) {
                let isMatch = true
                
                for (let i = 0; i < routeParts.length; i++) {
                    if (routeParts[i].startsWith(':')) {
                        // Parameter
                        const paramName = routeParts[i].slice(1)
                        params[paramName] = pathParts[i]
                    } else if (routeParts[i] !== pathParts[i]) {
                        // Not a match
                        isMatch = false
                        break
                    }
                }
                
                if (isMatch) {
                    return params
                }
            }
        }
        
        return {}
    }
    
    back() {
        if (this.history.length > 0) {
            const previousRoute = this.history.pop()
            this.navigate(previousRoute.route, {}, false)
        } else {
            // Default back behavior
            this.navigate('dashboard')
        }
    }
    
    getCurrentRoute() {
        return this.currentRoute
    }
    
    getRouteConfig(route) {
        return this.routes.get(route)
    }
    
    isCurrentRoute(route) {
        return this.currentRoute === route
    }
    
    // Check if route requires authentication
    requiresAuth(route) {
        const routeConfig = this.routes.get(route)
        return routeConfig ? routeConfig.requiresAuth : true
    }
    
    // Get breadcrumb for current route
    getBreadcrumb() {
        const routeConfig = this.routes.get(this.currentRoute)
        if (!routeConfig) return []
        
        const breadcrumb = []
        
        // Add dashboard as root for authenticated routes
        if (routeConfig.requiresAuth && this.currentRoute !== 'dashboard') {
            breadcrumb.push({
                name: 'Dashboard',
                route: 'dashboard'
            })
        }
        
        // Add current route
        breadcrumb.push({
            name: routeConfig.title,
            route: this.currentRoute,
            current: true
        })
        
        return breadcrumb
    }
    
    // Clear history
    clearHistory() {
        this.history = []
    }
}

export const router = new Router()
