import Alpine from 'alpinejs'
import { apiService } from '../../services/api.js'

Alpine.data('programDetail', () => ({
    // Program data
    program: null,
    books: [],
    progress: {},
    team: null,
    
    // Component state
    isLoading: true,
    error: null,
    activeTab: 'overview',
    
    // Get program ID from route
    get programId() {
        return this.$store.router?.getRouteParams()?.id
    },
    
    async init() {
        console.log('🎯 Program Detail component initialized')
        
        if (!this.programId) {
            this.error = 'Program ID not found'
            this.isLoading = false
            return
        }
        
        await this.loadProgramData()
    },
    
    async loadProgramData() {
        this.isLoading = true
        this.error = null
        
        try {
            // Load program details
            const programResponse = await apiService.getStudentProgramDetail(this.programId)
            this.program = programResponse.program
            
            // Load books and progress in parallel
            const [booksResponse, progressResponse] = await Promise.all([
                apiService.getStudentBooks(this.programId),
                apiService.getStudentProgress(this.programId)
            ])
            
            this.books = booksResponse.books || []
            this.progress = progressResponse.progress || {}
            
            // Try to load team info
            try {
                const teamResponse = await apiService.getStudentTeam(this.programId)
                this.team = teamResponse.team
            } catch (error) {
                console.log('No team data available')
            }
            
            console.log('🎮 Program data loaded successfully')
        } catch (error) {
            console.error('🚨 Failed to load program data:', error)
            this.error = 'Failed to load program details. Please try again.'
        } finally {
            this.isLoading = false
        }
    },
    
    // Tab management
    setActiveTab(tab) {
        this.activeTab = tab
        
        // Add tab switch animation
        const tabContent = this.$refs.tabContent
        if (tabContent) {
            tabContent.classList.add('animate-fade-in')
            setTimeout(() => {
                tabContent.classList.remove('animate-fade-in')
            }, 300)
        }
    },
    
    isActiveTab(tab) {
        return this.activeTab === tab
    },
    
    // Navigation
    goBack() {
        this.$store.app.navigateTo('dashboard')
    },
    
    // Book actions
    async startReading(book) {
        try {
            await apiService.startReading(book.id)
            this.$store.app.showGameToast(`Started reading "${book.title}"! 📖`, 'success')
            
            // Refresh progress
            await this.loadProgramData()
        } catch (error) {
            console.error('🚨 Failed to start reading:', error)
            this.$store.app.showGameToast('Failed to start reading. Please try again.', 'error')
        }
    },
    
    async completeReading(book) {
        try {
            await apiService.completeReading(book.id)
            this.$store.app.showGameToast(`Completed "${book.title}"! Great job! 🎉`, 'success')
            
            // Add points for completion
            this.$store.app.addPoints(book.points || 100)
            
            // Check for achievements
            this.$store.app.checkAchievements('book_completed', { book })
            
            // Refresh progress
            await this.loadProgramData()
        } catch (error) {
            console.error('🚨 Failed to complete reading:', error)
            this.$store.app.showGameToast('Failed to mark as complete. Please try again.', 'error')
        }
    },
    
    // Utility methods
    formatDate(date) {
        if (!date) return 'N/A'
        return new Date(date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        })
    },
    
    getStatusBadge() {
        if (!this.program) return 'badge-ghost'
        return this.program.active ? 'badge-success' : 'badge-error'
    },
    
    getStatusText() {
        if (!this.program) return 'Unknown'
        return this.program.active ? 'Active' : 'Inactive'
    },
    
    getOverallProgress() {
        if (!this.program || !this.program.total_books || this.program.total_books === 0) {
            return 0
        }
        return Math.round((this.program.completed_books / this.program.total_books) * 100)
    },
    
    // Book helpers
    hasBooks() {
        return this.books.length > 0
    },
    
    getBookProgress(book) {
        return this.progress[book.id] || 0
    },
    
    isBookCompleted(book) {
        return this.getBookProgress(book) >= 100
    },
    
    isBookStarted(book) {
        return this.getBookProgress(book) > 0
    },
    
    getBookStatusIcon(book) {
        if (this.isBookCompleted(book)) return '✅'
        if (this.isBookStarted(book)) return '📖'
        return '📚'
    },
    
    getBookStatusText(book) {
        if (this.isBookCompleted(book)) return 'Completed'
        if (this.isBookStarted(book)) return 'In Progress'
        return 'Not Started'
    },
    
    getBookStatusColor(book) {
        if (this.isBookCompleted(book)) return 'text-green-600'
        if (this.isBookStarted(book)) return 'text-blue-600'
        return 'text-gray-600'
    },
    
    // Team helpers
    hasTeam() {
        return this.team && this.team.members && this.team.members.length > 0
    },
    
    getTeamMemberCount() {
        return this.team?.members?.length || 0
    },
    
    // Program theme
    getProgramIcon() {
        if (!this.program) return '📚'
        
        const icons = ['📚', '🌟', '🎯', '🏆', '🎨', '🔬', '🌍', '🎭', '🎵', '⚡']
        const iconIndex = (this.program.id || 0) % icons.length
        return icons[iconIndex]
    },
    
    getProgramThemeColor() {
        if (!this.program) return 'from-purple-400 to-pink-400'
        
        const colors = [
            'from-blue-400 to-cyan-400',
            'from-purple-400 to-pink-400',
            'from-green-400 to-emerald-400',
            'from-orange-400 to-red-400',
            'from-indigo-400 to-purple-400',
            'from-teal-400 to-blue-400'
        ]
        
        const colorIndex = (this.program.id || 0) % colors.length
        return colors[colorIndex]
    },
    
    // Get next book to read
    getNextBook() {
        return this.books.find(book => !this.isBookCompleted(book))
    },
    
    // Get completed books count
    getCompletedBooksCount() {
        return this.books.filter(book => this.isBookCompleted(book)).length
    },
    
    // Get total points earned
    getTotalPointsEarned() {
        return this.books
            .filter(book => this.isBookCompleted(book))
            .reduce((total, book) => total + (book.points || 0), 0)
    },
    
    // Quick actions
    async continueReading() {
        const nextBook = this.getNextBook()
        if (nextBook) {
            if (this.isBookStarted(nextBook)) {
                // Continue reading
                this.$store.app.showGameToast(`Continuing "${nextBook.title}"! 📖`, 'info')
            } else {
                // Start new book
                await this.startReading(nextBook)
            }
        }
    }
}))

// Export template for reference
export const programDetailTemplate = `
<!-- Program detail template will be added to index.html -->
`
