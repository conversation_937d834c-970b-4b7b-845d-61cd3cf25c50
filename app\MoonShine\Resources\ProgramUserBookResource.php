<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Book;
use App\Models\Program;
use App\Models\ProgramUserBook;
use App\Models\TermUser;
use App\MoonShine\Resources\BaseResource;
use App\MoonShine\Resources\BookResource;
use App\MoonShine\Resources\ProgramResource;
use App\MoonShine\Resources\TermUserResource;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Fields\Date;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Components\Tabs;
use MoonShine\UI\Components\Tabs\Tab;

/**
 * @extends BaseResource<ProgramUserBook>
 */
class ProgramUserBookResource extends BaseResource
{
    protected string $model = ProgramUserBook::class;

    protected string $column = 'start_date';

    protected array $with = ['program', 'termUser', 'termUser.user', 'book', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.program_user_books');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name)
                ->sortable(),
            
            BelongsTo::make(__('admin.students'), 'termUser', 
                formatted: fn(TermUser $termUser) => $termUser->user->name)
                ->sortable(),
            
            Text::make(__('admin.student_class'), 'termUser.schoolClass.full_name'),
            
            BelongsTo::make(__('admin.books'), 'book', 
                formatted: fn(Book $book) => $book->name)
                ->sortable(),
            
            Text::make(__('admin.isbn'), 'book.isbn'),
            
            Date::make(__('admin.start_date'), 'start_date')
                ->format('d.m.Y')
                ->sortable(),
            
            Date::make(__('admin.end_date'), 'end_date')
                ->format('d.m.Y')
                ->sortable(),
            /*
            Text::make(__('admin.status'), function($item) {
                return match($item->status) {
                    'completed' => __('admin.completed'),
                    'overdue' => __('admin.overdue'),
                    'ongoing' => __('admin.ongoing'),
                    default => __('admin.unknown')
                };
            })->badge(fn($value, $item) => $item->status_color),
            
            Text::make(__('admin.duration_days'), function($item) {
                if ($item->is_completed) {
                    return $item->duration . ' ' . __('admin.days');
                } else {
                    return $item->days_since_start . ' ' . __('admin.days_ongoing');
                }
            }),
            */
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Tabs::make([
                    Tab::make(__('admin.assignment_info'), [
                        Flex::make([
                            BelongsTo::make(__('admin.programs'), 'program', 
                                formatted: fn(Program $program) => $program->name,
                                resource: ProgramResource::class)
                                ->required()
                                ->placeholder(__('admin.select_program'))
                                ->asyncSearch('name'),
                            
                            BelongsTo::make(__('admin.students'), 'termUser', 
                                formatted: fn(TermUser $termUser) => $termUser->user->name . ' (' . $termUser->schoolClass?->full_name . ')',
                                resource: TermUserResource::class)
                                ->required()
                                ->placeholder(__('admin.select_student'))
                                ->asyncSearch('user.name')
                                ->valuesQuery(function ($query) {
                                    // Only show students
                                    return $query->students();
                                }),
                        ]),
                        
                        BelongsTo::make(__('admin.books'), 'book', 
                            formatted: fn(Book $book) => $book->name . ' (' . $book->isbn . ')',
                            resource: BookResource::class)
                            ->required()
                            ->placeholder(__('admin.select_book'))
                            ->asyncSearch('name'),
                    ]),
                    
                    Tab::make(__('admin.dates'), [
                        Flex::make([
                            Date::make(__('admin.start_date'), 'start_date')
                                ->required()
                                ->default(now()->toDateString())
                                ->hint(__('admin.assignment_date_hint')),
                            
                            Date::make(__('admin.end_date'), 'end_date')
                                ->nullable()
                                ->hint(__('admin.completion_date_hint')),
                        ]),
                    ]),
                ]),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            ...parent::getCommonDetailFields(),
            
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name),
            
            BelongsTo::make(__('admin.students'), 'termUser', 
                formatted: fn(TermUser $termUser) => $termUser->user->name),
            
            Text::make(__('admin.student_email'), 'termUser.user.email'),
            Text::make(__('admin.student_class'), 'termUser.schoolClass.full_name'),
            Text::make(__('admin.student_organization'), 'termUser.organization.name'),
            
            BelongsTo::make(__('admin.books'), 'book', 
                formatted: fn(Book $book) => $book->name),
            
            Text::make(__('admin.isbn'), 'book.isbn'),
            Text::make(__('admin.publishers'), 'book.publisher.name'),
            Text::make(__('admin.page_count'), 'book.page_count'),
            
            Date::make(__('admin.start_date'), 'start_date')
                ->format('d.m.Y'),
            
            Date::make(__('admin.end_date'), 'end_date')
                ->format('d.m.Y'),
            /*
            Text::make(__('admin.status'), function($item) {
                return match($item->status) {
                    'completed' => __('admin.completed'),
                    'overdue' => __('admin.overdue'),
                    'ongoing' => __('admin.ongoing'),
                    default => __('admin.unknown')
                };
            })->badge(fn($value, $item) => $item->status_color),
            
            Text::make(__('admin.duration_days'), function($item) {
                if ($item->is_completed) {
                    return $item->duration . ' ' . __('admin.days');
                } else {
                    return $item->days_since_start . ' ' . __('admin.days_ongoing');
                }
            }),
            
            Text::make(__('admin.is_completed'), function($item) {
                return $item->is_completed ? __('admin.yes') : __('admin.no');
            })->badge(fn($value) => $value === __('admin.yes') ? 'green' : 'gray'),
            
            Text::make(__('admin.is_overdue'), function($item) {
                return $item->is_overdue ? __('admin.yes') : __('admin.no');
            })->badge(fn($value) => $value === __('admin.yes') ? 'red' : 'gray'),
            */
        ];
    }

    protected function filters(): iterable
    {
        return [
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name,
                resource: ProgramResource::class),
            
            BelongsTo::make(__('admin.students'), 'termUser', 
                formatted: fn(TermUser $termUser) => $termUser->user->name,
                resource: TermUserResource::class),
            
            BelongsTo::make(__('admin.books'), 'book', 
                formatted: fn(Book $book) => $book->name,
                resource: BookResource::class),
        ];
    }

    public function rules(mixed $item): array
    {
        $programId = request('program_id') ?? $item?->program_id;
        $termUserId = request('term_user_id') ?? $item?->term_user_id;
        $bookId = request('book_id') ?? $item?->book_id;
        
        return [
            'program_id' => ['required', 'exists:programs,id'],
            'term_user_id' => [
                'required', 
                'exists:term_users,id',
                // Ensure the term user is a student
                function ($attribute, $value, $fail) {
                    $termUser = TermUser::find($value);
                    if ($termUser && !$termUser->isStudent()) {
                        $fail(__('admin.must_be_student'));
                    }
                }
            ],
            'book_id' => [
                'required', 
                'exists:books,id',
                // Ensure book is not already assigned to this student in this program
                function ($attribute, $value, $fail) use ($item, $programId, $termUserId) {
                    if ($programId && $termUserId) {
                        $query = ProgramUserBook::where('program_id', $programId)
                                                ->where('term_user_id', $termUserId)
                                                ->where('book_id', $value);
                        
                        if ($item && $item->exists) {
                            $query->where('id', '!=', $item->id);
                        }
                        
                        if ($query->exists()) {
                            $fail(__('admin.book_already_assigned'));
                        }
                    }
                }
            ],
            'start_date' => ['required', 'date'],
            'end_date' => [
                'nullable', 
                'date',
                'after_or_equal:start_date'
            ],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return ['termUser.user.name', 'book.name', 'book.isbn', 'program.name'];
    }

    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }

    protected function checkCreatePermission($user): bool
    {
        // System admins, group school admins, school admins, and teachers can create assignments
        return $user->isSystemAdmin() || 
               $user->hasRoleLevel(\App\Models\Role::LEVEL_GROUP_SCHOOL_ADMIN) ||
               $user->hasRoleLevel(\App\Models\Role::LEVEL_SCHOOL_ADMIN) ||
               $user->hasRoleLevel(\App\Models\Role::LEVEL_TEACHER);
    }

    protected function checkUpdatePermission($user): bool
    {
        // System admins, group school admins, school admins, and teachers can update assignments
        return $user->isSystemAdmin() || 
               $user->hasRoleLevel(\App\Models\Role::LEVEL_GROUP_SCHOOL_ADMIN) ||
               $user->hasRoleLevel(\App\Models\Role::LEVEL_SCHOOL_ADMIN) ||
               $user->hasRoleLevel(\App\Models\Role::LEVEL_TEACHER);
    }

    protected function checkDeletePermission($user): bool
    {
        // System admins, group school admins, and school admins can delete assignments
        return $user->isSystemAdmin() || 
               $user->hasRoleLevel(\App\Models\Role::LEVEL_GROUP_SCHOOL_ADMIN) ||
               $user->hasRoleLevel(\App\Models\Role::LEVEL_SCHOOL_ADMIN);
    }
}
