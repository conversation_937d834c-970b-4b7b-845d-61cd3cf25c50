<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Book;
use App\Models\Publisher;
use App\Models\Author;
use App\Models\Role;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Number;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Laravel\Fields\Relationships\BelongsToMany;
use MoonShine\Support\Attributes\Icon;
use MoonShine\UI\Components\Tabs;
use MoonShine\UI\Components\Tabs\Tab;


#[Icon('book-open')]

class BookResource extends BaseResource
{
    protected string $model = Book::class;

    protected string $column = 'name';

    protected array $with = ['publisher', 'authors', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.books');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            Text::make(__('admin.name'), 'name')
                ->sortable(),
            
            Text::make(__('admin.isbn'), 'isbn')
                ->sortable(),
            
            BelongsTo::make(__('admin.publisher'), 'publisher', 
                formatted: fn(Publisher $publisher) => $publisher->name)
                ->sortable(),
            
            Text::make(__('admin.author_names'), 'author_names'),
            
            Number::make(__('admin.page_count'), 'page_count')
                ->sortable(),
            
            Number::make(__('admin.year_of_publish'), 'year_of_publish')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                        Text::make(__('admin.name'), 'name')
                            ->required()
                            ->placeholder(__('admin.enter_name')),
                        
                        Flex::make([
                            Text::make(__('admin.isbn'), 'isbn')
                                ->required()
                                ->placeholder(__('admin.enter_isbn')),
                            
                            BelongsTo::make(__('admin.publisher'), 'publisher', 
                                formatted: fn(Publisher $publisher) => $publisher->name,
                                resource: PublisherResource::class)
                                ->required()
                                ->placeholder(__('admin.select_publisher')),
                        ]),
                        
                        Flex::make([
                            Number::make(__('admin.page_count'), 'page_count')
                                ->required()
                                ->min(1)
                                ->placeholder(__('admin.enter_page_count')),
                            
                            Number::make(__('admin.year_of_publish'), 'year_of_publish')
                                ->required()
                                ->min(1900)
                                ->max(intval(date('Y')))
                                ->placeholder(__('admin.enter_year')),
                        ]),
                        BelongsToMany::make(__('admin.authors'), 'authors', resource: AuthorResource::class, formatted: 'name')
                            ->selectMode(),
                    ]),
            
            
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.name'), 'name'),
            Text::make(__('admin.isbn'), 'isbn'),
            Text::make(__('admin.publisher'), 'publisher.name'),
            Number::make(__('admin.page_count'), 'page_count'),
            Number::make(__('admin.year_of_publish'), 'year_of_publish'),
            Text::make(__('admin.difficulty'), 'difficulty'),
            Text::make(__('admin.estimated_reading_time'), 'estimated_reading_time'),
            Text::make(__('admin.publication_info'), 'publication_info'),
            Text::make(__('admin.age'), 'age'),
            Text::make(__('admin.authors'), 'authors'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'isbn' => ['required', 'string', 'max:255', 'unique:books,isbn,' . $item?->id],
            'publisher_id' => ['required', 'exists:publishers,id'],
            'page_count' => ['required', 'integer', 'min:1'],
            'year_of_publish' => ['required', 'integer', 'min:1900', 'max:' . date('Y')],
            ...parent::getCommonRules($item),
        ];
    }

    protected function checkCreatePermission($user): bool
    {
        // System admins and school admins can create books
        return $user->isSystemAdmin() ||
               $user->hasRoleLevel(Role::LEVEL_SCHOOL_ADMIN);
    }

    protected function checkUpdatePermission($user): bool
    {
        // System admins and school admins can update books
        return $user->isSystemAdmin() ||
               $user->hasRoleLevel(Role::LEVEL_SCHOOL_ADMIN);
    }

    protected function checkDeletePermission($user): bool
    {
        // System admins and school admins can delete books
        return $user->isSystemAdmin() ||
               $user->hasRoleLevel(Role::LEVEL_SCHOOL_ADMIN);
    }

    protected function getSearchFields(): array
    {
        return ['name', 'isbn'];
    }

    protected function getDefaultSort(): array
    {
        return ['name' => 'asc'];
    }
}
