<?php

use Illuminate\Support\Facades\Route;
use App\MoonShine\Pages\PrivacyAgreementPage;

Route::get('/', function () {
    return view('welcome');
});

// Privacy Agreement Routes
Route::middleware(['web'])->group(function () {
    Route::get('/admin/privacy-agreement', [PrivacyAgreementPage::class, 'handle'])
        ->name('moonshine.privacy-agreement');

    Route::post('/admin/process-privacy-consent', [PrivacyAgreementPage::class, 'processPrivacyConsent'])
        ->name('moonshine.process-privacy-consent');
});
