import Alpine from 'alpinejs'

Alpine.data('bottomNav', () => ({
    // Navigation items
    navItems: [
        {
            route: 'dashboard',
            label: 'Dashboard',
            icon: 'dashboard'
        },
        {
            route: 'programs',
            label: 'Programs',
            icon: 'programs'
        },
        {
            route: 'students',
            label: 'Students',
            icon: 'students'
        },
        {
            route: 'profile',
            label: 'Profile',
            icon: 'profile'
        }
    ],
    
    init() {
        // Any initialization logic
    },
    
    // Navigate to route
    navigateTo(route) {
        this.$store.app.navigateTo(route)
    },
    
    // Check if route is active
    isActive(route) {
        return this.$store.app.currentRoute === route
    },
    
    // Get icon SVG for navigation item
    getIcon(iconName) {
        const icons = {
            dashboard: `
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6a2 2 0 01-2 2H10a2 2 0 01-2-2V5z" />
                </svg>
            `,
            programs: `
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
            `,
            students: `
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
            `,
            profile: `
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
            `
        }
        
        return icons[iconName] || icons.dashboard
    }
}))

// Export template for reference (template is embedded in index.html)
export const bottomNavTemplate = `
<!-- Bottom navigation template is embedded in index.html -->
`
