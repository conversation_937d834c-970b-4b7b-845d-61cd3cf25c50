import Alpine from 'alpinejs'

Alpine.data('appLayout', () => ({
    // Layout state
    showSidebar: false,
    isRefreshing: false,
    lastRefresh: null,
    
    // Pull to refresh
    pullStartY: 0,
    pullCurrentY: 0,
    isPulling: false,
    pullThreshold: 80,
    
    // Celebration state
    showCelebration: false,
    celebrationType: '',
    celebrationData: {},
    
    init() {
        // Set up pull to refresh
        this.setupPullToRefresh()
        
        // Set up auto refresh
        this.setupAutoRefresh()
        
        // Listen for game celebrations
        this.setupGameEventListeners()
        
        // Handle orientation change
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.handleOrientationChange()
            }, 100)
        })
        
        console.log('🎮 App Layout initialized')
    },
    
    setupPullToRefresh() {
        let startY = 0
        let currentY = 0
        let isScrolledToTop = true
        
        const container = this.$refs.mainContent
        if (!container) return
        
        // Check if scrolled to top
        container.addEventListener('scroll', () => {
            isScrolledToTop = container.scrollTop === 0
        })
        
        // Touch start
        container.addEventListener('touchstart', (e) => {
            if (!isScrolledToTop) return
            
            startY = e.touches[0].clientY
            this.pullStartY = startY
        }, { passive: true })
        
        // Touch move
        container.addEventListener('touchmove', (e) => {
            if (!isScrolledToTop || this.isRefreshing) return
            
            currentY = e.touches[0].clientY
            const pullDistance = currentY - startY
            
            if (pullDistance > 0) {
                this.isPulling = true
                this.pullCurrentY = Math.min(pullDistance, this.pullThreshold * 1.5)
                
                // Update pull indicator
                const indicator = this.$refs.pullIndicator
                if (indicator) {
                    indicator.style.transform = `translateY(${this.pullCurrentY}px)`
                    indicator.style.opacity = Math.min(this.pullCurrentY / this.pullThreshold, 1)
                    
                    // Add rainbow effect when threshold reached
                    if (this.pullCurrentY >= this.pullThreshold) {
                        indicator.classList.add('animate-glow')
                    } else {
                        indicator.classList.remove('animate-glow')
                    }
                }
            }
        }, { passive: true })
        
        // Touch end
        container.addEventListener('touchend', () => {
            if (!this.isPulling) return
            
            if (this.pullCurrentY >= this.pullThreshold) {
                this.triggerRefresh()
            }
            
            this.resetPullToRefresh()
        }, { passive: true })
    },
    
    resetPullToRefresh() {
        this.isPulling = false
        this.pullCurrentY = 0
        
        const indicator = this.$refs.pullIndicator
        if (indicator) {
            indicator.style.transform = 'translateY(-100%)'
            indicator.style.opacity = '0'
            indicator.classList.remove('animate-glow')
        }
    },
    
    async triggerRefresh() {
        if (this.isRefreshing) return
        
        this.isRefreshing = true
        this.lastRefresh = new Date()
        
        try {
            // Emit refresh event
            this.$dispatch('refresh-data')
            
            // Add some visual flair
            this.createRefreshSparkles()
            
            // Minimum refresh time for UX
            await new Promise(resolve => setTimeout(resolve, 1500))
            
            // Show success message
            this.$store.app.showGameToast('Data refreshed! ✨', 'success')
        } catch (error) {
            console.error('🚨 Refresh error:', error)
            this.$store.app.showGameToast('Refresh failed 😞', 'error')
        } finally {
            this.isRefreshing = false
            this.resetPullToRefresh()
        }
    },
    
    createRefreshSparkles() {
        const container = this.$refs.mainContent
        if (!container) return
        
        for (let i = 0; i < 8; i++) {
            setTimeout(() => {
                const sparkle = document.createElement('div')
                sparkle.className = 'absolute w-3 h-3 bg-gradient-to-r from-primary to-secondary rounded-full animate-ping pointer-events-none z-50'
                sparkle.style.left = Math.random() * 100 + '%'
                sparkle.style.top = Math.random() * 50 + '%'
                
                container.appendChild(sparkle)
                
                setTimeout(() => sparkle.remove(), 1000)
            }, i * 100)
        }
    },
    
    setupAutoRefresh() {
        // Auto refresh every 10 minutes when app is visible
        setInterval(() => {
            if (!document.hidden && this.$store.app.isAuthenticated) {
                this.triggerRefresh()
            }
        }, 10 * 60 * 1000)
    },
    
    setupGameEventListeners() {
        // Listen for game celebrations
        window.addEventListener('game-celebration', (event) => {
            this.handleGameCelebration(event.detail)
        })
    },
    
    handleGameCelebration(detail) {
        this.celebrationType = detail.type
        this.celebrationData = detail.data || {}
        this.showCelebration = true
        
        // Auto-hide celebration after 3 seconds
        setTimeout(() => {
            this.showCelebration = false
        }, 3000)
        
        // Create visual effects based on celebration type
        if (detail.type === 'levelup') {
            this.createLevelUpEffect()
        } else if (detail.type === 'achievement') {
            this.createAchievementEffect()
        }
    },
    
    createLevelUpEffect() {
        // Create a burst of colorful particles
        const colors = ['bg-yellow-400', 'bg-orange-400', 'bg-red-400', 'bg-pink-400', 'bg-purple-400']
        
        for (let i = 0; i < 15; i++) {
            setTimeout(() => {
                const particle = document.createElement('div')
                particle.className = `absolute w-4 h-4 ${colors[i % colors.length]} rounded-full animate-bounce pointer-events-none z-50`
                particle.style.left = Math.random() * 100 + '%'
                particle.style.top = Math.random() * 100 + '%'
                
                document.body.appendChild(particle)
                
                setTimeout(() => particle.remove(), 2000)
            }, i * 50)
        }
    },
    
    createAchievementEffect() {
        // Create golden sparkles
        for (let i = 0; i < 12; i++) {
            setTimeout(() => {
                const sparkle = document.createElement('div')
                sparkle.className = 'absolute w-3 h-3 bg-yellow-400 rounded-full animate-sparkle pointer-events-none z-50'
                sparkle.style.left = Math.random() * 100 + '%'
                sparkle.style.top = Math.random() * 100 + '%'
                sparkle.style.boxShadow = '0 0 10px rgba(251, 191, 36, 0.8)'
                
                document.body.appendChild(sparkle)
                
                setTimeout(() => sparkle.remove(), 1500)
            }, i * 75)
        }
    },
    
    handleOrientationChange() {
        // Handle any layout adjustments needed on orientation change
        this.showSidebar = false
    },
    
    toggleSidebar() {
        this.showSidebar = !this.showSidebar
    },
    
    closeSidebar() {
        this.showSidebar = false
    },
    
    // Navigation methods
    navigateTo(route) {
        this.closeSidebar()
        this.$store.app.navigateTo(route)
    },
    
    logout() {
        this.closeSidebar()
        this.$store.app.logout()
    },
    
    // Get user display info
    getUserDisplayName() {
        return this.$store.app.getUserDisplayName()
    },
    
    getUserFirstName() {
        const fullName = this.getUserDisplayName()
        return fullName.split(' ')[0]
    },
    
    getUserAvatarColor() {
        return this.$store.app.getUserAvatar()
    },
    
    getUserInitials() {
        const name = this.getUserDisplayName()
        const parts = name.split(' ')
        if (parts.length >= 2) {
            return (parts[0].charAt(0) + parts[1].charAt(0)).toUpperCase()
        }
        return name.charAt(0).toUpperCase()
    },
    
    // Format last refresh time
    getLastRefreshTime() {
        if (!this.lastRefresh) return ''
        
        const now = new Date()
        const diff = now - this.lastRefresh
        const minutes = Math.floor(diff / 60000)
        
        if (minutes < 1) return 'Just now'
        if (minutes === 1) return '1 minute ago'
        return `${minutes} minutes ago`
    },
    
    // Get celebration message
    getCelebrationMessage() {
        if (this.celebrationType === 'levelup') {
            const level = this.celebrationData.level || this.$store.app.userLevel
            return `🎊 Level ${level}! 🎊`
        } else if (this.celebrationType === 'achievement') {
            return `🏆 ${this.celebrationData.name || 'Achievement Unlocked!'} 🏆`
        }
        return '🎉 Awesome! 🎉'
    },
    
    // Close celebration
    closeCelebration() {
        this.showCelebration = false
    }
}))

// Export template for reference
export const appLayoutTemplate = `
<!-- App layout template is embedded in index.html -->
`
