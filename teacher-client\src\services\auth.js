import { apiService } from './api.js'
import { storageService } from './storage.js'

class AuthService {
    constructor() {
        this.user = null
        this.isAuthenticated = false
    }
    
    async login(credentials) {
        try {
            console.log('Attempting login...')
            
            const response = await apiService.login({
                email: credentials.email,
                password: credentials.password
            })
            
            if (response.token && response.user) {
                // Store token and user data
                storageService.setToken(response.token)
                storageService.setUser(response.user)
                
                this.user = response.user
                this.isAuthenticated = true
                
                console.log('Login successful:', response.user)
                
                return {
                    success: true,
                    user: response.user,
                    token: response.token
                }
            } else {
                console.error('Invalid login response:', response)
                return {
                    success: false,
                    message: response.message || 'Invalid login response'
                }
            }
        } catch (error) {
            console.error('Login error:', error)
            
            if (error.status === 401) {
                return {
                    success: false,
                    message: 'Invalid email or password'
                }
            } else if (error.status === 422) {
                return {
                    success: false,
                    message: 'Please check your email and password',
                    errors: error.errors
                }
            } else if (error.status === 0) {
                return {
                    success: false,
                    message: 'Unable to connect to server. Please check your internet connection.'
                }
            } else {
                return {
                    success: false,
                    message: error.message || 'Login failed. Please try again.'
                }
            }
        }
    }
    
    async logout() {
        try {
            // Call logout endpoint if we have a token
            if (storageService.getToken()) {
                await apiService.logout()
            }
        } catch (error) {
            console.error('Logout API call failed:', error)
            // Continue with local logout even if API call fails
        } finally {
            // Clear local storage
            this.clearAuthData()
        }
    }
    
    clearAuthData() {
        storageService.clearToken()
        storageService.clearUser()
        this.user = null
        this.isAuthenticated = false
        console.log('Auth data cleared')
    }
    
    async getCurrentUser() {
        try {
            // First check if we have a stored user
            const storedUser = storageService.getUser()
            if (storedUser) {
                this.user = storedUser
                this.isAuthenticated = true
                return storedUser
            }
            
            // If no stored user but we have a token, fetch from API
            const token = storageService.getToken()
            if (token) {
                const response = await apiService.getCurrentUser()
                if (response.user) {
                    storageService.setUser(response.user)
                    this.user = response.user
                    this.isAuthenticated = true
                    return response.user
                }
            }
            
            return null
        } catch (error) {
            console.error('Get current user error:', error)
            this.clearAuthData()
            return null
        }
    }
    
    async refreshToken() {
        try {
            const response = await apiService.refreshToken()
            if (response.token) {
                storageService.setToken(response.token)
                return response.token
            }
            return null
        } catch (error) {
            console.error('Token refresh failed:', error)
            this.clearAuthData()
            return null
        }
    }
    
    isLoggedIn() {
        return this.isAuthenticated && !!storageService.getToken()
    }
    
    getUser() {
        return this.user || storageService.getUser()
    }
    
    getToken() {
        return storageService.getToken()
    }
    
    // Check if user has teacher role
    isTeacher() {
        const user = this.getUser()
        return user && user.role && user.role.level === 3 // Teacher role level
    }
    
    // Get user's role name
    getUserRole() {
        const user = this.getUser()
        return user?.role?.name || 'Unknown'
    }
    
    // Get user's display name
    getUserDisplayName() {
        const user = this.getUser()
        if (!user) return 'Unknown User'
        
        if (user.title && user.name) {
            return `${user.title} ${user.name}`
        }
        return user.name || user.email || 'Unknown User'
    }
    
    // Validate token expiration (if JWT contains exp claim)
    isTokenExpired() {
        const token = this.getToken()
        if (!token) return true
        
        try {
            // Decode JWT payload (basic check, not cryptographically verified)
            const payload = JSON.parse(atob(token.split('.')[1]))
            const currentTime = Math.floor(Date.now() / 1000)
            return payload.exp < currentTime
        } catch (error) {
            console.error('Token validation error:', error)
            return true
        }
    }
}

export const authService = new AuthService()
