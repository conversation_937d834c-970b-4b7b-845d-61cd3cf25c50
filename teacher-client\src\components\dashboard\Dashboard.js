import Alpine from 'alpinejs'
import { apiService } from '../../services/api.js'

Alpine.data('dashboard', () => ({
    // Dashboard data
    programs: [],
    activeTerm: null,
    stats: {
        totalPrograms: 0,
        activePrograms: 0,
        totalStudents: 0,
        totalBooks: 0
    },
    
    // Component state
    isLoading: true,
    error: null,
    lastUpdated: null,
    
    async init() {
        console.log('Dashboard component initialized')
        await this.loadDashboardData()
        
        // Listen for refresh events
        this.$watch('$store.app.currentRoute', (newRoute) => {
            if (newRoute === 'dashboard') {
                this.refreshData()
            }
        })
    },
    
    async loadDashboardData() {
        this.isLoading = true
        this.error = null
        
        try {
            // Load data in parallel
            const [programsResponse, termResponse] = await Promise.all([
                this.loadPrograms(),
                this.loadActiveTerm()
            ])
            
            this.calculateStats()
            this.lastUpdated = new Date()
            
            console.log('Dashboard data loaded successfully')
        } catch (error) {
            console.error('Failed to load dashboard data:', error)
            this.error = 'Failed to load dashboard data. Please try again.'
            this.$store.app.showToast('Failed to load dashboard data', 'error')
        } finally {
            this.isLoading = false
        }
    },
    
    async loadPrograms() {
        try {
            const response = await apiService.getTeacherPrograms()
            this.programs = response.programs || []
            return response
        } catch (error) {
            console.error('Failed to load programs:', error)

            // Check if we're in demo mode or if API is not available
            const urlParams = new URLSearchParams(window.location.search)
            const isDemoMode = urlParams.has('demo') || import.meta.env.VITE_DEMO_MODE === 'true'

            if (isDemoMode || error.status === 0) {
                console.log('🎭 Loading demo data for programs')
                this.programs = this.getDemoPrograms()
                return { programs: this.programs }
            }

            this.programs = []
            throw error
        }
    },
    
    async loadActiveTerm() {
        try {
            const response = await apiService.getActiveTerm()
            this.activeTerm = response.term || null
            return response
        } catch (error) {
            console.error('Failed to load active term:', error)

            // Provide demo data for active term
            const urlParams = new URLSearchParams(window.location.search)
            const isDemoMode = urlParams.has('demo') || import.meta.env.VITE_DEMO_MODE === 'true'

            if (isDemoMode || error.status === 0) {
                this.activeTerm = {
                    id: 1,
                    name: '2024-2025 Academic Year',
                    start_date: '2024-09-01',
                    end_date: '2025-06-30'
                }
            } else {
                this.activeTerm = null
            }
        }
    },
    
    calculateStats() {
        this.stats.totalPrograms = this.programs.length
        this.stats.activePrograms = this.programs.filter(p => p.active).length
        this.stats.totalStudents = this.programs.reduce((sum, p) => sum + (p.student_count || 0), 0)
        this.stats.totalBooks = this.programs.reduce((sum, p) => sum + (p.book_count || 0), 0)
    },
    
    async refreshData() {
        await this.loadDashboardData()
    },
    
    // Navigation methods
    viewProgram(programId) {
        this.$store.app.navigateTo('program', { id: programId })
    },
    
    // Utility methods
    formatDate(date) {
        if (!date) return 'N/A'
        return new Date(date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        })
    },
    
    getGreeting() {
        const hour = new Date().getHours()
        if (hour < 12) return 'Good morning'
        if (hour < 17) return 'Good afternoon'
        return 'Good evening'
    },
    
    getUserName() {
        return this.$store.app.user?.name || 'Teacher'
    },
    
    getActiveTermName() {
        return this.activeTerm?.name || 'No active term'
    },
    
    // Program filtering and sorting
    getActivePrograms() {
        return this.programs.filter(program => program.active)
    },
    
    getInactivePrograms() {
        return this.programs.filter(program => !program.active)
    },
    
    // Handle empty states
    hasPrograms() {
        return this.programs.length > 0
    },
    
    hasActivePrograms() {
        return this.getActivePrograms().length > 0
    },

    // Demo data for testing
    getDemoPrograms() {
        return [
            {
                id: 1,
                name: 'Summer Reading Challenge 2024',
                description: 'An exciting summer reading program for students to explore new worlds through books.',
                active: true,
                start_date: '2024-06-01',
                end_date: '2024-08-31',
                student_count: 45,
                book_count: 12,
                completed_books: 8,
                total_books: 12,
                progress: 67
            },
            {
                id: 2,
                name: 'Classic Literature Journey',
                description: 'Discover timeless stories and classic literature with guided reading activities.',
                active: true,
                start_date: '2024-09-01',
                end_date: '2024-12-15',
                student_count: 32,
                book_count: 8,
                completed_books: 3,
                total_books: 8,
                progress: 38
            },
            {
                id: 3,
                name: 'Science Fiction Adventure',
                description: 'Explore the future and beyond with exciting science fiction novels.',
                active: false,
                start_date: '2024-01-15',
                end_date: '2024-05-30',
                student_count: 28,
                book_count: 10,
                completed_books: 10,
                total_books: 10,
                progress: 100
            }
        ]
    }
}))

// Export template for reference (template is embedded in index.html)
export const dashboardTemplate = `
<!-- Dashboard template is embedded in index.html -->
<!-- Dashboard template is embedded in index.html -->
`
