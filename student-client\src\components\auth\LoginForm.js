import Alpine from 'alpinejs'

Alpine.data('loginForm', () => ({
    // Form data
    email: '',
    password: '',
    rememberMe: false,
    
    // Form state
    isLoading: false,
    errors: {},
    showPassword: false,
    
    // Initialize component
    init() {
        // Focus on email input when component loads
        this.$nextTick(() => {
            this.$refs.emailInput?.focus()
        })
        
        // Check for saved email
        const savedEmail = localStorage.getItem('reading_quest_saved_email')
        if (savedEmail) {
            this.email = savedEmail
            this.rememberMe = true
        }
        
        // Add floating animation to decorative elements
        this.animateFloatingElements()
    },
    
    animateFloatingElements() {
        // Add random delays to floating elements for more natural movement
        const floatingElements = document.querySelectorAll('.animate-float, .animate-bounce-slow, .animate-pulse-slow, .animate-wiggle')
        floatingElements.forEach((element, index) => {
            element.style.animationDelay = `${index * 0.5}s`
        })
    },
    
    // Form validation
    validateForm() {
        this.errors = {}
        
        if (!this.email) {
            this.errors.email = 'Email is required'
        } else if (!this.isValidEmail(this.email)) {
            this.errors.email = 'Please enter a valid email address'
        }
        
        if (!this.password) {
            this.errors.password = 'Password is required'
        } else if (this.password.length < 6) {
            this.errors.password = 'Password must be at least 6 characters'
        }
        
        return Object.keys(this.errors).length === 0
    },
    
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        return emailRegex.test(email)
    },
    
    // Handle form submission
    async handleSubmit() {
        if (!this.validateForm()) {
            this.shakeForm()
            return
        }
        
        this.isLoading = true
        this.errors = {}
        
        try {
            const result = await this.$store.app.login({
                email: this.email.trim(),
                password: this.password
            })
            
            if (result.success) {
                // Save email if remember me is checked
                if (this.rememberMe) {
                    localStorage.setItem('reading_quest_saved_email', this.email)
                } else {
                    localStorage.removeItem('reading_quest_saved_email')
                }
                
                // Show success animation
                this.showSuccessAnimation()
                
                // Clear form
                this.clearForm()
            } else {
                // Handle login error
                if (result.errors) {
                    this.errors = result.errors
                } else {
                    this.errors.general = result.message || 'Login failed'
                }
                
                // Show error animation
                this.shakeForm()
            }
        } catch (error) {
            console.error('🚨 Login form error:', error)
            this.errors.general = 'An unexpected error occurred'
            this.shakeForm()
        } finally {
            this.isLoading = false
        }
    },
    
    // Clear form data
    clearForm() {
        this.password = ''
        this.errors = {}
        this.showPassword = false
    },
    
    // Toggle password visibility
    togglePasswordVisibility() {
        this.showPassword = !this.showPassword
    },
    
    // Handle input changes to clear errors
    clearError(field) {
        if (this.errors[field]) {
            delete this.errors[field]
        }
        if (this.errors.general) {
            delete this.errors.general
        }
    },
    
    // Handle Enter key press
    handleKeyPress(event) {
        if (event.key === 'Enter') {
            this.handleSubmit()
        }
    },
    
    // Get error message for field
    getError(field) {
        return this.errors[field] || ''
    },
    
    // Check if field has error
    hasError(field) {
        return !!this.errors[field]
    },
    
    // Get input class based on error state
    getInputClass(field) {
        const baseClass = 'input-game'
        return this.hasError(field) ? `${baseClass} border-red-400 focus:border-red-500` : baseClass
    },
    
    // Animation methods
    shakeForm() {
        const form = this.$el.querySelector('form')
        if (form) {
            form.classList.add('animate-wiggle')
            setTimeout(() => {
                form.classList.remove('animate-wiggle')
            }, 1000)
        }
    },
    
    showSuccessAnimation() {
        // Add sparkle effect to the form
        const form = this.$el.querySelector('.card')
        if (form) {
            // Create sparkles
            for (let i = 0; i < 10; i++) {
                setTimeout(() => {
                    this.createSparkle(form)
                }, i * 100)
            }
        }
    },
    
    createSparkle(container) {
        const sparkle = document.createElement('div')
        sparkle.className = 'absolute w-2 h-2 bg-yellow-400 rounded-full animate-ping pointer-events-none'
        sparkle.style.left = Math.random() * 100 + '%'
        sparkle.style.top = Math.random() * 100 + '%'
        sparkle.style.zIndex = '1000'
        
        container.style.position = 'relative'
        container.appendChild(sparkle)
        
        setTimeout(() => {
            sparkle.remove()
        }, 1000)
    },
    
    // Get random encouraging message
    getEncouragingMessage() {
        const messages = [
            "Ready for an adventure? 🚀",
            "Let's explore new worlds! 🌟",
            "Time to unlock your potential! 💫",
            "Your reading journey awaits! 📚",
            "Adventure is calling! 🎯"
        ]
        return messages[Math.floor(Math.random() * messages.length)]
    }
}))

// Export template for reference (not used in this implementation)
export const loginFormTemplate = `
<!-- Login form template is embedded in index.html -->
`
