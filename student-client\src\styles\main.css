@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom CSS for gamified mobile design */

/* Base styles */
@layer base {
  html {
    font-size: 16px;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
  }
  
  body {
    font-family: 'Fredoka', 'Inter', system-ui, sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* Safe area handling for mobile devices */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .safe-area-left {
    padding-left: env(safe-area-inset-left);
  }
  
  .safe-area-right {
    padding-right: env(safe-area-inset-right);
  }
}

/* Game-specific component styles */
@layer components {
  /* Game-style buttons */
  .btn-game {
    @apply btn font-bold text-white shadow-lg transform transition-all duration-200 border-0;
    background: linear-gradient(135deg, theme('colors.primary.500'), theme('colors.secondary.500'));
  }

  .btn-game:hover {
    background: linear-gradient(135deg, theme('colors.primary.600'), theme('colors.secondary.600'));
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    transform: scale(1.05);
  }
  
  .btn-game-secondary {
    @apply btn font-semibold shadow-md transform transition-all duration-200;
    background: linear-gradient(135deg, theme('colors.purple.100'), theme('colors.pink.100'));
    color: theme('colors.primary.700');
    border: 2px solid theme('colors.primary.200');
  }

  .btn-game-secondary:hover {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    transform: scale(1.05);
  }
  
  /* Game cards */
  .card-game {
    @apply backdrop-blur-sm shadow-xl border-2 rounded-2xl overflow-hidden;
    background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(248,250,252,0.9));
    border-color: rgba(217, 70, 239, 0.2);
  }

  .card-game:hover {
    @apply shadow-2xl transform scale-105;
    transition: all 0.3s ease;
    border-color: rgba(217, 70, 239, 0.4);
  }
  
  /* Progress bars with game styling */
  .progress-game {
    @apply w-full bg-gray-200 rounded-full h-4 overflow-hidden shadow-inner;
  }
  
  .progress-game-fill {
    @apply h-full rounded-full transition-all duration-500 ease-out;
    background: linear-gradient(90deg, theme('colors.primary.400'), theme('colors.secondary.400'));
    box-shadow: 0 0 10px rgba(217, 70, 239, 0.5);
  }
  
  /* Achievement badges */
  .badge-achievement {
    @apply inline-flex items-center gap-2 px-3 py-2 rounded-full font-bold text-sm shadow-lg;
    background: linear-gradient(135deg, theme('colors.gold.400'), theme('colors.gold.500'));
    color: theme('colors.gold.900');
    border: 2px solid theme('colors.gold.600');
  }
  
  .badge-achievement.locked {
    @apply bg-gray-300 text-gray-600 border-gray-400;
    background: linear-gradient(135deg, theme('colors.gray.300'), theme('colors.gray.400'));
  }
  
  /* Level indicators */
  .level-indicator {
    @apply inline-flex items-center justify-center w-12 h-12 rounded-full font-bold text-white shadow-lg;
    background: linear-gradient(135deg, theme('colors.gem.400'), theme('colors.gem.600'));
    border: 3px solid theme('colors.gem.300');
  }
  
  /* Points display */
  .points-display {
    @apply inline-flex items-center gap-2 px-4 py-2 rounded-full font-bold shadow-lg;
    background: linear-gradient(135deg, theme('colors.yellow.300'), theme('colors.yellow.400'));
    color: theme('colors.yellow.900');
    border: 2px solid theme('colors.yellow.500');
  }
  
  /* Mobile-optimized form inputs */
  .input-game {
    @apply input input-bordered w-full text-base border-2 transition-all duration-200;
    background-color: rgba(255, 255, 255, 0.8);
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .input-game:focus {
    background-color: rgba(255, 255, 255, 1);
    border-color: #d946ef; /* primary color */
  }
  
  /* Container for mobile */
  .container-mobile {
    @apply container mx-auto px-4 max-w-mobile;
  }
  
  /* Game-style notifications */
  .notification-game {
    @apply fixed top-4 right-4 backdrop-blur-sm border-2 rounded-xl p-4 shadow-xl z-50;
    background-color: rgba(255, 255, 255, 0.95);
    border-color: rgba(217, 70, 239, 0.3);
    animation: slideInRight 0.3s ease-out;
  }
  
  /* Floating action elements */
  .floating-element {
    @apply absolute pointer-events-none;
    animation: float 3s ease-in-out infinite;
  }
  
  /* Sparkle effects */
  .sparkle {
    @apply absolute w-2 h-2 bg-yellow-400 rounded-full;
    animation: sparkle 1.5s ease-in-out infinite;
  }
  
  /* Game UI panels */
  .game-panel {
    @apply backdrop-blur-sm border-2 rounded-2xl p-6 shadow-xl;
    background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(250,245,255,0.95));
    border-color: rgba(217, 70, 239, 0.2);
  }
  
  /* Character avatars */
  .avatar-game {
    @apply w-16 h-16 rounded-full border-4 border-white shadow-lg;
    background: linear-gradient(135deg, theme('colors.primary.400'), theme('colors.secondary.400'));
  }

  /* Book covers */
  .book-cover {
    @apply relative overflow-hidden rounded-lg shadow-lg border-2;
    aspect-ratio: 3/4;
    border-color: rgba(217, 70, 239, 0.2);
  }

  .book-cover::before {
    content: '';
    @apply absolute inset-0 pointer-events-none;
    background: linear-gradient(135deg, transparent, rgba(0,0,0,0.2));
  }

  /* Skeleton loading styles */
  .skeleton-mobile {
    @apply bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-pulse;
    background-size: 200% 100%;
    animation: shimmer 1.5s ease-in-out infinite;
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
}

/* Utility classes */
@layer utilities {
  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* Touch manipulation */
  .touch-manipulation {
    touch-action: manipulation;
  }
  
  /* Prevent text selection on UI elements */
  .select-none-touch {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
  }
  
  /* Smooth scrolling */
  .scroll-smooth-mobile {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }
  
  /* Game-specific gradients */
  .bg-game-primary {
    background: linear-gradient(135deg, theme('colors.primary.500'), theme('colors.secondary.500'));
  }
  
  .bg-game-secondary {
    background: linear-gradient(135deg, theme('colors.purple.100'), theme('colors.pink.100'));
  }
  
  .bg-game-success {
    background: linear-gradient(135deg, theme('colors.green.400'), theme('colors.emerald.500'));
  }
  
  .bg-game-warning {
    background: linear-gradient(135deg, theme('colors.yellow.400'), theme('colors.orange.500'));
  }
  
  /* Text gradients */
  .text-game-gradient {
    @apply text-transparent bg-clip-text;
    background: linear-gradient(135deg, theme('colors.primary.600'), theme('colors.secondary.600'));
  }
}

/* Animation classes */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bounce-in {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes wiggle {
  0%, 100% { transform: rotate(-3deg); }
  50% { transform: rotate(3deg); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes sparkle {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.5; transform: scale(1.2); }
}

@keyframes glow {
  0% { box-shadow: 0 0 5px rgba(217, 70, 239, 0.5); }
  100% { box-shadow: 0 0 20px rgba(217, 70, 239, 0.8); }
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.animate-slide-in-up {
  animation: slideInUp 0.3s ease-out;
}

.animate-slide-in-down {
  animation: slideInDown 0.3s ease-out;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-bounce-in {
  animation: bounce-in 0.5s ease-out;
}

.animate-wiggle {
  animation: wiggle 1s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-sparkle {
  animation: sparkle 1.5s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

/* Responsive design for different screen sizes */
@media (max-width: 480px) {
  .container-mobile {
    @apply px-3;
  }
  
  .card-game {
    @apply rounded-xl;
  }
  
  .game-panel {
    @apply p-4 rounded-xl;
  }
}

/* Dark mode support (if needed later) */
@media (prefers-color-scheme: dark) {
  .card-game {
    background: linear-gradient(135deg, rgba(30,41,59,0.9), rgba(51,65,85,0.9));
  }
  
  .game-panel {
    background: linear-gradient(135deg, rgba(30,41,59,0.95), rgba(51,65,85,0.95));
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card-game {
    @apply border-4;
  }
  
  .btn-game {
    @apply border-2 border-primary-700;
  }
}
