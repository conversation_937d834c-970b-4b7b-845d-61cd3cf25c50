<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    
    <title><?php echo $__env->yieldContent('title', __('teacher.app_title')); ?></title>
    <meta name="description" content="<?php echo $__env->yieldContent('description', __('teacher.app_description')); ?>">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#7843E9">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="<?php echo e(__('teacher.app_title')); ?>">
    
    <!-- PWA Icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/teacher-manifest.json">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Styles -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    
    <!-- MoonShine Styles -->
    <link rel="stylesheet" href="<?php echo e(asset('vendor/moonshine/assets/main.css')); ?>">
    
    <style>
        /* Mobile-first responsive design */
        .teacher-container {
            max-width: 100%;
            margin: 0 auto;
            padding: 1rem;
        }
        
        @media (min-width: 768px) {
            .teacher-container {
                max-width: 768px;
                padding: 2rem;
            }
        }
        
        /* Touch-friendly buttons */
        .btn-touch {
            min-height: 44px;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
            cursor: pointer;
            user-select: none;
            -webkit-tap-highlight-color: transparent;
        }
        
        .btn-touch:active {
            transform: scale(0.98);
        }
        
        /* Card touch effects */
        .card-touch {
            transition: all 0.2s ease;
            cursor: pointer;
            user-select: none;
            -webkit-tap-highlight-color: transparent;
        }
        
        .card-touch:active {
            transform: scale(0.99);
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }
        
        /* Mobile navigation */
        .mobile-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e5e7eb;
            padding: 0.5rem 0;
            z-index: 50;
        }
        
        .safe-area-bottom {
            padding-bottom: env(safe-area-inset-bottom);
        }
        
        /* Purple theme integration */
        .text-purple-600 {
            color: #7843E9 !important;
        }
        
        .bg-purple-600 {
            background-color: #7843E9 !important;
        }
        
        .border-purple-600 {
            border-color: #7843E9 !important;
        }
        
        .bg-purple-100 {
            background-color: rgba(120, 67, 233, 0.1) !important;
        }
        
        .bg-purple-50 {
            background-color: rgba(120, 67, 233, 0.05) !important;
        }
        
        /* Loading states */
        .loading-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }
        
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        /* Toast notifications */
        .toast {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%) translateY(-100px);
            background: #333;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            z-index: 1000;
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .toast.show {
            opacity: 1;
            transform: translateX(-50%) translateY(-10px);
        }
        
        /* Pull to refresh */
        .pull-to-refresh {
            position: relative;
            overflow: hidden;
        }
        
        .pull-indicator {
            position: absolute;
            top: -60px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        
        .pull-indicator.visible {
            top: 20px;
        }
    </style>
    
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body class="antialiased bg-gray-50">
    <!-- Loading overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center">
        <div class="bg-white rounded-lg p-6 text-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
            <p class="text-gray-600"><?php echo e(__('teacher.loading')); ?></p>
        </div>
    </div>
    
    <!-- Toast notification -->
    <div id="toast" class="toast"></div>
    
    <!-- Main content -->
    <main class="min-h-screen pb-20 pull-to-refresh" id="main-content">
        <!-- Pull to refresh indicator -->
        <div class="pull-indicator" id="pull-indicator">
            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
        </div>
        
        <?php echo $__env->yieldContent('content'); ?>
    </main>
    
    <!-- Mobile navigation (only show on authenticated pages) -->
    <?php if(auth()->guard('moonshine')->check()): ?>
        <nav class="mobile-nav safe-area-bottom">
            <div class="flex justify-around items-center">
                <a href="<?php echo e(route('teacher.dashboard')); ?>" class="flex flex-col items-center p-2 text-gray-600 hover:text-purple-600 transition-colors">
                    <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                    </svg>
                    <span class="text-xs"><?php echo e(__('teacher.dashboard')); ?></span>
                </a>
                
                <a href="#" class="flex flex-col items-center p-2 text-gray-600 hover:text-purple-600 transition-colors">
                    <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253z"></path>
                    </svg>
                    <span class="text-xs"><?php echo e(__('teacher.students')); ?></span>
                </a>
                
                <a href="#" class="flex flex-col items-center p-2 text-gray-600 hover:text-purple-600 transition-colors">
                    <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <span class="text-xs"><?php echo e(__('teacher.reports')); ?></span>
                </a>
                
                <form method="POST" action="<?php echo e(route('teacher.logout')); ?>" class="inline">
                    <?php echo csrf_field(); ?>
                    <button type="submit" class="flex flex-col items-center p-2 text-gray-600 hover:text-red-600 transition-colors">
                        <svg class="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                        </svg>
                        <span class="text-xs"><?php echo e(__('teacher.logout')); ?></span>
                    </button>
                </form>
            </div>
        </nav>
    <?php endif; ?>

    <!-- Scripts -->
    <script>
        // Show/hide loading overlay
        function showLoading() {
            document.getElementById('loading-overlay').classList.remove('hidden');
            document.getElementById('loading-overlay').classList.add('flex');
        }

        function hideLoading() {
            document.getElementById('loading-overlay').classList.add('hidden');
            document.getElementById('loading-overlay').classList.remove('flex');
        }

        // Toast notification
        function showToast(message, type = 'info') {
            const toast = document.getElementById('toast');
            toast.textContent = message;
            toast.classList.add('show');

            // Auto hide after 3 seconds
            setTimeout(() => {
                toast.classList.remove('show');
            }, 3000);
        }

        // Pull to refresh functionality
        let startY = 0;
        let currentY = 0;
        let pullDistance = 0;
        let isPulling = false;
        let isRefreshing = false;

        const mainContent = document.getElementById('main-content');
        const pullIndicator = document.getElementById('pull-indicator');

        mainContent.addEventListener('touchstart', (e) => {
            if (window.scrollY === 0 && !isRefreshing) {
                startY = e.touches[0].clientY;
                isPulling = true;
            }
        });

        mainContent.addEventListener('touchmove', (e) => {
            if (isPulling && !isRefreshing) {
                currentY = e.touches[0].clientY;
                pullDistance = currentY - startY;

                if (pullDistance > 0) {
                    e.preventDefault();
                    const opacity = Math.min(pullDistance / 100, 1);
                    pullIndicator.style.opacity = opacity;

                    if (pullDistance > 60) {
                        pullIndicator.classList.add('visible');
                    } else {
                        pullIndicator.classList.remove('visible');
                    }
                }
            }
        });

        mainContent.addEventListener('touchend', () => {
            if (isPulling && pullDistance > 60 && !isRefreshing) {
                isRefreshing = true;
                pullIndicator.classList.add('visible');

                // Simulate refresh
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                pullIndicator.classList.remove('visible');
                pullIndicator.style.opacity = 0;
            }

            isPulling = false;
            pullDistance = 0;
        });

        // Handle form submissions with loading states
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function() {
                    showLoading();
                });
            });

            // Add touch feedback to buttons
            const touchButtons = document.querySelectorAll('.btn-touch, .card-touch');
            touchButtons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.98)';
                });

                button.addEventListener('touchend', function() {
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });

        // Service Worker registration for PWA
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/teacher-sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful');
                    })
                    .catch(function(err) {
                        console.log('ServiceWorker registration failed: ', err);
                    });
            });
        }

        // Haptic feedback simulation
        function hapticFeedback() {
            if (navigator.vibrate) {
                navigator.vibrate(10);
            }
        }

        // Add haptic feedback to interactive elements
        document.addEventListener('DOMContentLoaded', function() {
            const interactiveElements = document.querySelectorAll('button, .btn-touch, .card-touch, a[href]');
            interactiveElements.forEach(element => {
                element.addEventListener('touchstart', hapticFeedback);
            });
        });
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH D:\ba\calisma\web\kitapokuma\okuokuoku\code\moonaug\resources\views/layouts/teacher.blade.php ENDPATH**/ ?>