<?php

namespace App\MoonShine\Resources;

use App\Models\StoryRule;
use App\Models\Story;
use App\Models\Role;
use MoonShine\Support\Attributes\Icon;
use MoonShine\UI\Fields\{Text, Number, Select};
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Laravel\Fields\Relationships\HasMany;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Components\Tabs;
use MoonShine\UI\Components\Tabs\Tab;

#[Icon('cog')]
class StoryRuleResource extends BaseResource
{
    protected string $model = StoryRule::class;

    protected string $column = 'description';

    protected array $with = ['story', 'details', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.story_rules');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            BelongsTo::make(__('admin.story'), 'story', 
                formatted: fn(Story $story) => $story->title,
                resource: StoryResource::class)
                ->sortable(),
            
            Text::make(__('admin.rule_type'), 'rule_type_name')
                ->sortable(),
            
            Number::make(__('admin.quantity'), 'quantity')
                ->sortable(),
            
            Text::make(__('admin.description'), 'description'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Tabs::make([
                    Tab::make(__('admin.main_information'), [
                        BelongsTo::make(__('admin.story'), 'story', 
                            formatted: fn(Story $story) => $story->title,
                            resource: StoryResource::class)
                            ->required()
                            ->placeholder(__('admin.select_story')),
                        
                        Select::make(__('admin.rule_type'), 'rule_type')
                            ->required()
                            ->options(StoryRule::getRuleTypes())
                            ->placeholder(__('admin.select_rule_type')),
                        
                        Number::make(__('admin.quantity'), 'quantity')
                            ->min(0)
                            ->default(0)
                            ->placeholder(__('admin.enter_quantity'))
                            ->hint('Required for points, achievement count, and book count rules'),
                    ]),
                    
                    Tab::make(__('admin.summary'), [
                        Text::make(__('admin.description'), 'description')
                            ->readonly(),
                    ]),
                ]),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(__('admin.story'), 'story', 
                formatted: fn(Story $story) => $story->title,
                resource: StoryResource::class),
            Text::make(__('admin.rule_type'), 'rule_type_name'),
            Number::make(__('admin.quantity'), 'quantity'),
            Text::make(__('admin.description'), 'description'),
            HasMany::make(__('admin.story_rule_details'), 'details', 
                resource: StoryRuleDetailResource::class),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'story_id' => ['required', 'exists:stories,id'],
            'rule_type' => ['required', 'integer', 'min:1', 'max:7'],
            'quantity' => ['required', 'integer', 'min:0'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function checkCreatePermission($user): bool
    {
        // System admins, group school admins, and school admins can create rules
        return $user->isSystemAdmin() || 
               $user->hasRoleLevel(Role::LEVEL_GROUP_SCHOOL_ADMIN) ||
               $user->hasRoleLevel(Role::LEVEL_SCHOOL_ADMIN);
    }

    protected function checkUpdatePermission($user): bool
    {
        // System admins, group school admins, and school admins can update rules
        return $user->isSystemAdmin() || 
               $user->hasRoleLevel(Role::LEVEL_GROUP_SCHOOL_ADMIN) ||
               $user->hasRoleLevel(Role::LEVEL_SCHOOL_ADMIN);
    }

    protected function checkDeletePermission($user): bool
    {
        // Only system admins and group school admins can delete rules
        return $user->isSystemAdmin() || 
               $user->hasRoleLevel(Role::LEVEL_GROUP_SCHOOL_ADMIN);
    }

    protected function getSearchFields(): array
    {
        return [];
    }

    protected function getDefaultSort(): array
    {
        return ['id' => 'desc'];
    }
}
