# Reading Quest - Student Portal 🎮📚

A gamified mobile web application for students to track their reading progress and participate in educational reading programs. Built with modern web technologies and designed with game-like interfaces to engage young learners.

## ✨ Features

### 🎯 **Gamified Experience**
- **Level System**: Students earn XP and level up as they read
- **Achievement Badges**: Unlock achievements for reading milestones
- **Points & Rewards**: Earn points for completing books and tasks
- **Streak Tracking**: Maintain daily reading streaks for bonus rewards
- **Progress Visualization**: Beautiful progress bars and visual feedback

### 📱 **Mobile-First Design**
- **Touch-Optimized**: Large buttons and touch-friendly interactions
- **Pull-to-Refresh**: Native mobile refresh functionality
- **Responsive Layout**: Optimized for phones and tablets
- **Game-Style UI**: Colorful, engaging interface with animations
- **PWA Ready**: Can be installed on mobile devices like a native app

### 📚 **Reading Management**
- **Program Participation**: Join and track multiple reading programs
- **Book Library**: Browse and select books to read
- **Reading Progress**: Track progress through individual books
- **Task Completion**: Complete reading-related tasks and assignments
- **Team Features**: Participate in reading teams and challenges

### 🎨 **Visual Design**
- **Game-Inspired UI**: Bright colors, animations, and engaging graphics
- **Character Avatars**: Personalized student profiles with avatars
- **Achievement Gallery**: Visual showcase of unlocked badges
- **Celebration Effects**: Sparkles, animations, and visual rewards
- **Themed Programs**: Each reading program has unique visual themes

## 🛠 Tech Stack

- **Frontend Framework**: Alpine.js 3.x (lightweight, reactive)
- **Build Tool**: Vite 5.x (fast development and building)
- **Styling**: Tailwind CSS 3.x + DaisyUI 4.x (utility-first + components)
- **HTTP Client**: Axios (API communication)
- **Storage**: LocalStorage with service layer abstraction
- **PWA**: Web App Manifest + Service Worker ready
- **Fonts**: Fredoka (playful, game-like typography)

## 📁 Project Structure

```
student-client/
├── public/                    # Static assets
│   ├── manifest.json         # PWA manifest
│   ├── favicon.svg           # App icon
│   └── assets/               # Game assets (images, sounds)
├── src/
│   ├── components/           # Alpine.js components
│   │   ├── auth/            # Authentication components
│   │   ├── dashboard/       # Dashboard and home components
│   │   ├── layout/          # Layout and navigation components
│   │   └── program/         # Program detail components
│   ├── services/            # Business logic services
│   │   ├── api.js           # API communication
│   │   ├── auth.js          # Authentication service
│   │   ├── game.js          # Gamification service
│   │   ├── router.js        # Client-side routing
│   │   └── storage.js       # Local storage management
│   ├── styles/              # CSS and styling
│   │   └── main.css         # Main stylesheet with game styles
│   └── main.js              # Application entry point
├── scripts/                 # Development scripts
│   └── dev-setup.js         # Setup automation script
├── index.html               # Main HTML template
├── package.json             # Dependencies and scripts
├── tailwind.config.js       # Tailwind configuration
├── vite.config.js           # Vite build configuration
└── README.md               # This file
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- Laravel backend running on `http://localhost:8000`

### Installation

1. **Navigate to the student-client directory**
   ```bash
   cd student-client
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Run setup script**
   ```bash
   npm run setup
   ```

4. **Configure environment**
   Edit `.env` to match your Laravel backend:
   ```env
   VITE_API_BASE_URL=http://localhost:8000/api
   VITE_APP_NAME="Reading Quest"
   ```

5. **Start development server**
   ```bash
   npm run dev
   ```
   
   The app will be available at `http://localhost:3001`

### Build for Production

```bash
npm run build
```

The built files will be in the `dist/` directory.

## 🎮 Game Features

### 🏆 Achievement System
- **First Steps**: Earn your first 10 points
- **Bookworm**: Complete your first book
- **Streak Master**: Maintain reading streaks
- **Point Collector**: Reach point milestones
- **Reading Champion**: Complete multiple books

### 📈 Progression System
- **Experience Points**: Earned through reading activities
- **Level System**: Visual progression with level indicators
- **Progress Bars**: Animated progress tracking
- **Milestone Rewards**: Bonus points for achievements

### 🎨 Visual Effects
- **Celebration Animations**: Sparkles and particle effects
- **Level Up Effects**: Colorful animations for progression
- **Achievement Unlocks**: Special visual feedback
- **Interactive Elements**: Hover effects and micro-interactions

## 🔗 API Integration

The application connects to the Laravel backend with these endpoints:

### Authentication
- `POST /api/auth/login` - Student login
- `GET /api/auth/user` - Get current user
- `POST /api/auth/logout` - Logout

### Student Data
- `GET /api/student/programs` - Get assigned programs
- `GET /api/student/programs/{id}` - Get program details
- `GET /api/student/achievements` - Get achievements
- `GET /api/student/profile` - Get student profile

### Reading Activities
- `POST /api/student/books/{id}/start` - Start reading a book
- `POST /api/student/books/{id}/complete` - Complete a book
- `PUT /api/student/books/{id}/progress` - Update reading progress

### Game Data
- `GET /api/student/game-data` - Get game statistics
- `POST /api/student/points` - Update points
- `POST /api/student/achievements/{id}/unlock` - Unlock achievement

## 🎨 Design System

### Color Palette
- **Primary**: Purple gradient (#d946ef to #c026d3)
- **Secondary**: Orange gradient (#f97316 to #ea580c)
- **Success**: Green (#22c55e)
- **Warning**: Yellow/Orange (#f59e0b)
- **Game Gold**: Yellow (#fbbf24)
- **Game Gem**: Blue (#0ea5e9)

### Typography
- **Primary Font**: Fredoka (playful, rounded)
- **Fallback**: Inter (clean, readable)
- **Sizes**: Responsive scale from mobile to desktop

### Components
- **Game Cards**: Rounded corners, gradients, shadows
- **Buttons**: Large, colorful, with hover effects
- **Progress Bars**: Animated, gradient fills
- **Badges**: Achievement-style with icons
- **Avatars**: Colorful, character-based

## 📱 Mobile Optimization

### Touch Interactions
- **44px+ Touch Targets**: All interactive elements
- **Gesture Support**: Swipe navigation where appropriate
- **Haptic Feedback**: Vibration for actions (where supported)
- **Pull-to-Refresh**: Native mobile refresh pattern

### Performance
- **Lazy Loading**: Components loaded as needed
- **Image Optimization**: Responsive images with proper sizing
- **Caching**: LocalStorage for offline functionality
- **Bundle Splitting**: Optimized JavaScript bundles

### Accessibility
- **Screen Reader Support**: Proper ARIA labels
- **High Contrast**: Support for high contrast mode
- **Keyboard Navigation**: Full keyboard accessibility
- **Focus Management**: Clear focus indicators

## 🎯 Development Guidelines

### Adding New Components
1. Create component file in appropriate directory
2. Export Alpine.js data function
3. Import in `src/main.js`
4. Add to HTML template

### Game Asset Guidelines
- Use consistent cartoon/game art style
- Optimize for mobile (small file sizes)
- Include multiple resolutions
- Use bright, engaging colors

### Animation Best Practices
- Keep animations under 300ms for responsiveness
- Use CSS transforms for better performance
- Provide animation disable option
- Test on lower-end devices

## 🚀 Deployment

### Static Hosting
The built application can be deployed to:
- **Netlify**: Drag and drop `dist/` folder
- **Vercel**: Connect GitHub repository
- **GitHub Pages**: Use GitHub Actions
- **Firebase Hosting**: Use Firebase CLI

### Environment Configuration
Set production environment variables:
```env
VITE_API_BASE_URL=https://your-api-domain.com/api
VITE_DEV_MODE=false
```

## 🧪 Testing

### Manual Testing Checklist
- [ ] Login/logout functionality
- [ ] Dashboard data loading
- [ ] Program navigation
- [ ] Book reading workflow
- [ ] Achievement unlocking
- [ ] Mobile responsiveness
- [ ] Touch interactions
- [ ] Pull-to-refresh
- [ ] Offline behavior

### Browser Support
- Chrome/Safari on iOS 12+
- Chrome on Android 8+
- Modern desktop browsers

## 🤝 Contributing

1. Follow existing code structure and patterns
2. Use TypeScript-style JSDoc comments
3. Test on mobile devices
4. Maintain gamification elements
5. Follow mobile-first design principles

## 📄 License

This project is part of the gamified reading tracker system.

---

**Ready to embark on a reading adventure? 🚀📚✨**
