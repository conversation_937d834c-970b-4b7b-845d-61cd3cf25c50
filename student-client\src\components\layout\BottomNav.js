import Alpine from 'alpinejs'

Alpine.data('bottomNav', () => ({
    // Navigation items with game-style icons and descriptions
    navItems: [
        {
            route: 'dashboard',
            label: 'Home',
            icon: 'home',
            description: 'Your reading adventure starts here!'
        },
        {
            route: 'library',
            label: 'Library',
            icon: 'library',
            description: 'Discover amazing books to read'
        },
        {
            route: 'achievements',
            label: 'Badges',
            icon: 'achievements',
            description: 'See your awesome achievements'
        },
        {
            route: 'profile',
            label: 'Profile',
            icon: 'profile',
            description: 'Your reading hero profile'
        }
    ],
    
    // Animation state
    lastActiveRoute: '',
    animationClass: '',
    
    init() {
        // Watch for route changes to trigger animations
        this.$watch('$store.app.currentRoute', (newRoute, oldRoute) => {
            this.handleRouteChange(newRoute, oldRoute)
        })
        
        console.log('🧭 Bottom Navigation initialized')
    },
    
    handleRouteChange(newRoute, oldRoute) {
        this.lastActiveRoute = oldRoute
        
        // Add bounce animation to new active item
        this.$nextTick(() => {
            const activeButton = this.$el.querySelector(`[data-route="${newRoute}"]`)
            if (activeButton) {
                activeButton.classList.add('animate-bounce-in')
                setTimeout(() => {
                    activeButton.classList.remove('animate-bounce-in')
                }, 500)
            }
        })
    },
    
    // Navigate to route with haptic feedback
    navigateTo(route) {
        // Add haptic feedback if available
        if (navigator.vibrate) {
            navigator.vibrate(50)
        }
        
        // Add click animation
        const button = event.currentTarget
        button.classList.add('scale-95')
        setTimeout(() => {
            button.classList.remove('scale-95')
        }, 150)
        
        // Navigate
        this.$store.app.navigateTo(route)
        
        // Show encouraging message for certain routes
        this.showRouteMessage(route)
    },
    
    showRouteMessage(route) {
        const messages = {
            library: "Time to discover new adventures! 📚",
            achievements: "Look at all your amazing progress! 🏆",
            profile: "You're becoming a reading hero! 🦸‍♀️"
        }
        
        const message = messages[route]
        if (message && Math.random() < 0.3) { // 30% chance to show message
            setTimeout(() => {
                this.$store.app.showGameToast(message, 'info')
            }, 300)
        }
    },
    
    // Check if route is active
    isActive(route) {
        return this.$store.app.currentRoute === route
    },
    
    // Get icon SVG for navigation item
    getIcon(iconName) {
        const icons = {
            home: `
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6a2 2 0 01-2 2H10a2 2 0 01-2-2V5z" />
                </svg>
            `,
            library: `
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
            `,
            achievements: `
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                </svg>
            `,
            profile: `
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
            `
        }
        
        return icons[iconName] || icons.home
    },
    
    // Get button classes based on active state
    getButtonClasses(route) {
        const baseClasses = 'flex flex-col items-center gap-1 p-3 rounded-xl transition-all duration-200 transform'
        
        if (this.isActive(route)) {
            return `${baseClasses} text-primary bg-primary/10 scale-110 shadow-lg`
        } else {
            return `${baseClasses} text-base-content/60 hover:text-primary hover:bg-primary/5 hover:scale-105`
        }
    },
    
    // Get notification badge for routes (future feature)
    getNotificationBadge(route) {
        // This could show unread achievements, new books, etc.
        const badges = {
            achievements: this.getUnreadAchievements(),
            library: this.getNewBooks()
        }
        
        return badges[route] || 0
    },
    
    getUnreadAchievements() {
        // Placeholder for unread achievements count
        return 0
    },
    
    getNewBooks() {
        // Placeholder for new books count
        return 0
    },
    
    // Add floating animation to icons
    addFloatingAnimation(element) {
        element.classList.add('animate-float')
        setTimeout(() => {
            element.classList.remove('animate-float')
        }, 3000)
    },
    
    // Handle long press for additional options (future feature)
    handleLongPress(route) {
        // Could show quick actions or route description
        const item = this.navItems.find(item => item.route === route)
        if (item) {
            this.$store.app.showGameToast(item.description, 'info')
        }
    }
}))

// Export template for reference
export const bottomNavTemplate = `
<!-- Bottom navigation template is embedded in index.html -->
`
