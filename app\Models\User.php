<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'title',
        'email',
        'password',
        'role_id',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    /**
     * Get the user's role.
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

    /**
     * Get the user who created this record.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this record.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get term assignments for this user.
     */
    public function termUsers(): HasMany
    {
        return $this->hasMany(TermUser::class);
    }

    /**
     * Get active term assignments for this user.
     */
    public function activeTermUsers(): HasMany
    {
        return $this->hasMany(TermUser::class)->where('active', true);
    }

    /**
     * Get reading logs for this user.
     */
    public function readingLogs(): HasMany
    {
        return $this->hasMany(ProgramReadingLog::class);
    }

    /**
     * Check if user has a specific role level.
     */
    public function hasRoleLevel(int $level): bool
    {
        return $this->role?->level === $level;
    }

    /**
     * Check if user is system admin.
     */
    public function isSystemAdmin(): bool
    {
        return $this->hasRoleLevel(Role::LEVEL_SYSTEM_ADMIN);
    }

    /**
     * Check if user is school admin.
     */
    public function isSchoolAdmin(): bool
    {
        return $this->hasRoleLevel(Role::LEVEL_SCHOOL_ADMIN);
    }

    /**
     * Check if user is teacher.
     */
    public function isTeacher(): bool
    {
        return $this->hasRoleLevel(Role::LEVEL_TEACHER);
    }

    /**
     * Check if user is student.
     */
    public function isStudent(): bool
    {
        return $this->hasRoleLevel(Role::LEVEL_STUDENT);
    }

    /**
     * Check if user can manage another user.
     */
    public function canManage(User $user): bool
    {
        return $this->role && $user->role && $this->role->canManage($user->role);
    }

    /**
     * Get the display name for the user.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name;
    }

    /**
     * Get the user's agreements.
     */
    public function agreements(): HasMany
    {
        return $this->hasMany(UserAgreement::class);
    }

    /**
     * Check if user has accepted the current privacy policy.
     */
    public function hasAcceptedCurrentPrivacyPolicy(): bool
    {
        return UserAgreement::hasAcceptedCurrentPrivacyPolicy($this->id);
    }
}
