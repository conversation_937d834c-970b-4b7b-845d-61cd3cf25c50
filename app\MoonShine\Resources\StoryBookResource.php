<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Book;
use App\Models\Story;
use App\Models\StoryBook;
use App\MoonShine\Resources\BaseResource;
use App\MoonShine\Resources\BookResource;
use App\MoonShine\Resources\StoryResource;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Fields\Number;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;

/**
 * @extends BaseResource<StoryBook>
 */
class StoryBookResource extends BaseResource
{
    protected string $model = StoryBook::class;

    protected string $column = 'sequence';

    protected array $with = ['story', 'book', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.story_books');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            BelongsTo::make(__('admin.stories'), 'story', 
                formatted: fn(Story $story) => $story->title)
                ->sortable(),
            BelongsTo::make(__('admin.books'), 'book', 
                formatted: fn(Book $book) => $book->name)
                ->sortable(),
            Text::make(__('admin.isbn'), 'book.isbn'),
            Number::make(__('admin.sequence'), 'sequence')
                ->badge('blue')
                ->sortable(),
            Text::make(__('admin.publishers'), 'book.publisher.name'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(__('admin.stories'), 'story', 
                    formatted: fn(Story $story) => $story->title,
                    resource: StoryResource::class)
                    ->required()
                    ->placeholder(__('admin.select_story'))
                    ->asyncSearch('title'),
                
                BelongsTo::make(__('admin.books'), 'book', 
                    formatted: fn(Book $book) => $book->name . ' (' . $book->isbn . ')',
                    resource: BookResource::class)
                    ->required()
                    ->placeholder(__('admin.select_book'))
                    ->asyncSearch('name'),
                
                Number::make(__('admin.sequence'), 'sequence')
                    ->required()
                    ->min(0)
                    ->default(0)
                    ->hint(__('admin.sequence_hint')),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            ...parent::getCommonDetailFields(),
            BelongsTo::make(__('admin.stories'), 'story', 
                formatted: fn(Story $story) => $story->title),
            BelongsTo::make(__('admin.books'), 'book', 
                formatted: fn(Book $book) => $book->name),
            Text::make(__('admin.isbn'), 'book.isbn'),
            Text::make(__('admin.publishers'), 'book.publisher.name'),
            Number::make(__('admin.sequence'), 'sequence')
                ->badge('blue'),
            Text::make(__('admin.is_first'), function($item) {
                return $item->is_first ? __('admin.yes') : __('admin.no');
            })->badge(fn($value) => $value === __('admin.yes') ? 'green' : 'gray'),
            Text::make(__('admin.is_last'), function($item) {
                return $item->is_last ? __('admin.yes') : __('admin.no');
            })->badge(fn($value) => $value === __('admin.yes') ? 'green' : 'gray'),
        ];
    }

    protected function filters(): iterable
    {
        return [
            BelongsTo::make(__('admin.stories'), 'story', 
                formatted: fn(Story $story) => $story->title,
                resource: StoryResource::class),
            BelongsTo::make(__('admin.books'), 'book', 
                formatted: fn(Book $book) => $book->name,
                resource: BookResource::class),
        ];
    }

    public function rules(mixed $item): array
    {
        $storyId = request('story_id') ?? $item?->story_id;
        
        return [
            'story_id' => ['required', 'exists:stories,id'],
            'book_id' => [
                'required', 
                'exists:books,id',
                // Ensure book is not already associated with this story
                function ($attribute, $value, $fail) use ($item, $storyId) {
                    $query = StoryBook::where('story_id', $storyId)
                                    ->where('book_id', $value);
                    
                    if ($item && $item->exists) {
                        $query->where('id', '!=', $item->id);
                    }
                    
                    if ($query->exists()) {
                        $fail(__('admin.book_already_in_story'));
                    }
                }
            ],
            'sequence' => [
                'required', 
                'integer', 
                'min:0',
                // Ensure sequence is unique within the story
                function ($attribute, $value, $fail) use ($item, $storyId) {
                    $query = StoryBook::where('story_id', $storyId)
                                    ->where('sequence', $value);
                    
                    if ($item && $item->exists) {
                        $query->where('id', '!=', $item->id);
                    }
                    
                    if ($query->exists()) {
                        $fail(__('admin.sequence_already_exists'));
                    }
                }
            ],
            ...parent::getCommonRules($item),
        ];
    }

    protected function getSearchFields(): array
    {
        return ['story.title', 'book.name', 'book.isbn'];
    }

    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }

    protected function checkCreatePermission($user): bool
    {
        // System admins, group school admins, and school admins can create story books
        return $user->isSystemAdmin() || 
               $user->hasRoleLevel(\App\Models\Role::LEVEL_GROUP_SCHOOL_ADMIN) ||
               $user->hasRoleLevel(\App\Models\Role::LEVEL_SCHOOL_ADMIN);
    }

    protected function checkUpdatePermission($user): bool
    {
        // System admins, group school admins, and school admins can update story books
        return $user->isSystemAdmin() || 
               $user->hasRoleLevel(\App\Models\Role::LEVEL_GROUP_SCHOOL_ADMIN) ||
               $user->hasRoleLevel(\App\Models\Role::LEVEL_SCHOOL_ADMIN);
    }

    protected function checkDeletePermission($user): bool
    {
        // System admins and group school admins can delete story books
        return $user->isSystemAdmin() || 
               $user->hasRoleLevel(\App\Models\Role::LEVEL_GROUP_SCHOOL_ADMIN);
    }
}
