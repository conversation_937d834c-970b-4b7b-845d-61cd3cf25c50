import Alpine from 'alpinejs'

Alpine.data('appLayout', () => ({
    // Layout state
    showSidebar: false,
    isRefreshing: false,
    lastRefresh: null,
    
    // Pull to refresh
    pullStartY: 0,
    pullCurrentY: 0,
    isPulling: false,
    pullThreshold: 80,
    
    init() {
        // Set up pull to refresh
        this.setupPullToRefresh()
        
        // Set up auto refresh
        this.setupAutoRefresh()
        
        // Handle orientation change
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.handleOrientationChange()
            }, 100)
        })
    },
    
    setupPullToRefresh() {
        let startY = 0
        let currentY = 0
        let isScrolledToTop = true
        
        const container = this.$refs.mainContent
        if (!container) return
        
        // Check if scrolled to top
        container.addEventListener('scroll', () => {
            isScrolledToTop = container.scrollTop === 0
        })
        
        // Touch start
        container.addEventListener('touchstart', (e) => {
            if (!isScrolledToTop) return
            
            startY = e.touches[0].clientY
            this.pullStartY = startY
        }, { passive: true })
        
        // Touch move
        container.addEventListener('touchmove', (e) => {
            if (!isScrolledToTop || this.isRefreshing) return
            
            currentY = e.touches[0].clientY
            const pullDistance = currentY - startY
            
            if (pullDistance > 0) {
                this.isPulling = true
                this.pullCurrentY = Math.min(pullDistance, this.pullThreshold * 1.5)
                
                // Update pull indicator
                const indicator = this.$refs.pullIndicator
                if (indicator) {
                    indicator.style.transform = `translateY(${this.pullCurrentY}px)`
                    indicator.style.opacity = Math.min(this.pullCurrentY / this.pullThreshold, 1)
                }
            }
        }, { passive: true })
        
        // Touch end
        container.addEventListener('touchend', () => {
            if (!this.isPulling) return
            
            if (this.pullCurrentY >= this.pullThreshold) {
                this.triggerRefresh()
            }
            
            this.resetPullToRefresh()
        }, { passive: true })
    },
    
    resetPullToRefresh() {
        this.isPulling = false
        this.pullCurrentY = 0
        
        const indicator = this.$refs.pullIndicator
        if (indicator) {
            indicator.style.transform = 'translateY(-100%)'
            indicator.style.opacity = '0'
        }
    },
    
    async triggerRefresh() {
        if (this.isRefreshing) return
        
        this.isRefreshing = true
        this.lastRefresh = new Date()
        
        try {
            // Emit refresh event
            this.$dispatch('refresh-data')
            
            // Minimum refresh time for UX
            await new Promise(resolve => setTimeout(resolve, 1000))
            
            this.$store.app.showToast('Data refreshed', 'success')
        } catch (error) {
            console.error('Refresh error:', error)
            this.$store.app.showToast('Refresh failed', 'error')
        } finally {
            this.isRefreshing = false
            this.resetPullToRefresh()
        }
    },
    
    setupAutoRefresh() {
        // Auto refresh every 5 minutes when app is visible
        setInterval(() => {
            if (!document.hidden && this.$store.app.isAuthenticated) {
                this.triggerRefresh()
            }
        }, 5 * 60 * 1000)
    },
    
    handleOrientationChange() {
        // Handle any layout adjustments needed on orientation change
        this.showSidebar = false
    },
    
    toggleSidebar() {
        this.showSidebar = !this.showSidebar
    },
    
    closeSidebar() {
        this.showSidebar = false
    },
    
    // Navigation methods
    navigateTo(route) {
        this.closeSidebar()
        this.$store.app.navigateTo(route)
    },
    
    logout() {
        this.closeSidebar()
        this.$store.app.logout()
    },
    
    // Get user display name
    getUserDisplayName() {
        return this.$store.app.user?.name || 'Teacher'
    },
    
    getUserRole() {
        return this.$store.app.user?.role?.name || 'Teacher'
    },
    
    // Format last refresh time
    getLastRefreshTime() {
        if (!this.lastRefresh) return ''
        
        const now = new Date()
        const diff = now - this.lastRefresh
        const minutes = Math.floor(diff / 60000)
        
        if (minutes < 1) return 'Just now'
        if (minutes === 1) return '1 minute ago'
        return `${minutes} minutes ago`
    }
}))

// Export template for reference (template is embedded in index.html)
export const appLayoutTemplate = `
<!-- App layout template is embedded in index.html -->
<!-- App layout template is embedded in index.html -->
`
