import Alpine from 'alpinejs'

Alpine.data('programCard', () => ({
    // Program data
    program: null,
    
    init() {
        // Component initialization
        console.log('🎯 Program Card initialized')
    },
    
    setProgram(program) {
        this.program = program
    },
    
    // Navigation
    viewProgram() {
        if (this.program) {
            this.$store.app.navigateTo('program', { id: this.program.id })
        }
    },
    
    // Utility methods
    formatDate(date) {
        if (!date) return 'N/A'
        return new Date(date).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        })
    },
    
    getStatusBadge() {
        if (!this.program) return 'badge-ghost'
        return this.program.active ? 'badge-success' : 'badge-error'
    },
    
    getStatusText() {
        if (!this.program) return 'Unknown'
        return this.program.active ? 'Active' : 'Inactive'
    },
    
    getProgressPercentage() {
        if (!this.program || !this.program.total_books || this.program.total_books === 0) {
            return 0
        }
        return Math.round((this.program.completed_books / this.program.total_books) * 100)
    },
    
    hasBooks() {
        return this.program && this.program.book_count > 0
    },
    
    hasPoints() {
        return this.program && this.program.points > 0
    },
    
    getBookCountText() {
        const count = this.program?.book_count || 0
        return count === 1 ? '1 book' : `${count} books`
    },
    
    getPointsText() {
        const points = this.program?.points || 0
        return points === 1 ? '1 point' : `${points} points`
    },
    
    // Get program theme color based on name/type
    getThemeColor() {
        if (!this.program) return 'from-purple-400 to-pink-400'
        
        const colors = [
            'from-blue-400 to-cyan-400',
            'from-purple-400 to-pink-400',
            'from-green-400 to-emerald-400',
            'from-orange-400 to-red-400',
            'from-indigo-400 to-purple-400',
            'from-teal-400 to-blue-400'
        ]
        
        // Use program ID to consistently assign colors
        const colorIndex = (this.program.id || 0) % colors.length
        return colors[colorIndex]
    },
    
    // Get program icon based on type or theme
    getProgramIcon() {
        if (!this.program) return '📚'
        
        const icons = ['📚', '🌟', '🎯', '🏆', '🎨', '🔬', '🌍', '🎭', '🎵', '⚡']
        const iconIndex = (this.program.id || 0) % icons.length
        return icons[iconIndex]
    },
    
    // Get difficulty indicator
    getDifficultyLevel() {
        if (!this.program) return 1
        
        // This could be based on program data
        // For now, use a simple calculation
        const bookCount = this.program.book_count || 1
        if (bookCount <= 3) return 1
        if (bookCount <= 6) return 2
        return 3
    },
    
    getDifficultyStars() {
        const level = this.getDifficultyLevel()
        return '⭐'.repeat(level) + '☆'.repeat(3 - level)
    },
    
    // Animation helpers
    addHoverEffect() {
        // Add subtle animation on hover
        const element = this.$el
        if (element) {
            element.classList.add('animate-pulse')
            setTimeout(() => {
                element.classList.remove('animate-pulse')
            }, 200)
        }
    },
    
    // Get estimated reading time
    getEstimatedTime() {
        const bookCount = this.program?.book_count || 0
        if (bookCount === 0) return 'No books'
        
        const avgTimePerBook = 30 // minutes
        const totalMinutes = bookCount * avgTimePerBook
        
        if (totalMinutes < 60) {
            return `~${totalMinutes} min`
        } else {
            const hours = Math.round(totalMinutes / 60)
            return `~${hours} hour${hours > 1 ? 's' : ''}`
        }
    },
    
    // Check if program is new (created within last week)
    isNewProgram() {
        if (!this.program?.created_at) return false
        
        const createdDate = new Date(this.program.created_at)
        const weekAgo = new Date()
        weekAgo.setDate(weekAgo.getDate() - 7)
        
        return createdDate > weekAgo
    },
    
    // Get completion status message
    getCompletionMessage() {
        const progress = this.getProgressPercentage()
        
        if (progress === 0) return 'Ready to start! 🚀'
        if (progress < 25) return 'Just getting started! 💪'
        if (progress < 50) return 'Making great progress! 🌟'
        if (progress < 75) return 'More than halfway there! 🎯'
        if (progress < 100) return 'Almost finished! 🏁'
        return 'Completed! Amazing work! 🎉'
    }
}))

// Export template for reference
export const programCardTemplate = `
<!-- Program card template is embedded in the dashboard -->
`
