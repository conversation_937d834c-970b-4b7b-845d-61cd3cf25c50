import Alpine from 'alpinejs'
import { apiService } from './services/api.js'
import { authService } from './services/auth.js'
import { router } from './services/router.js'
import { storageService } from './services/storage.js'
import './styles/main.css'

// Import Alpine.js components
import './components/auth/LoginForm.js'
import './components/dashboard/Dashboard.js'
import './components/dashboard/ProgramCard.js'
import './components/layout/AppLayout.js'
import './components/layout/BottomNav.js'
import './components/program/ProgramDetail.js'

// Global Alpine.js data and methods
Alpine.data('app', () => ({
    // App state
    isLoading: true,
    isAuthenticated: false,
    user: null,
    currentRoute: 'login',
    
    // Initialize app
    async init() {
        console.log('Initializing Teacher App...')
        
        // Hide initial loader
        const loader = document.getElementById('initial-loader')
        if (loader) {
            loader.style.display = 'none'
        }
        
        // Initialize services
        await this.initializeServices()
        
        // Check authentication status
        await this.checkAuth()
        
        // Initialize router
        this.initializeRouter()
        
        this.isLoading = false
        
        console.log('App initialized successfully')
        console.log('🔍 Current state:', {
            isAuthenticated: this.isAuthenticated,
            currentRoute: this.currentRoute,
            user: this.user
        })
    },
    
    async initializeServices() {
        // Initialize API service with base URL
        apiService.init({
            baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
            timeout: 10000
        })
        
        // Set up API interceptors for auth
        apiService.setupAuthInterceptors(() => {
            this.logout()
        })
    },
    
    async checkAuth() {
        // Demo mode for testing - check if URL contains demo parameter or hash
        const urlParams = new URLSearchParams(window.location.search)
        const hashContainsDemo = window.location.hash.includes('demo')
        const isDemoMode = urlParams.has('demo') || hashContainsDemo || import.meta.env.VITE_DEMO_MODE === 'true'

        if (isDemoMode) {
            console.log('🎭 Running in demo mode')
            this.isAuthenticated = true
            this.user = {
                id: 1,
                name: 'Demo Teacher',
                email: '<EMAIL>',
                role: {
                    name: 'Teacher',
                    level: 3
                }
            }
            this.currentRoute = 'dashboard'
            return
        }

        const token = storageService.getToken()
        if (token) {
            try {
                const user = await authService.getCurrentUser()
                if (user) {
                    this.isAuthenticated = true
                    this.user = user
                    this.currentRoute = 'dashboard'
                    return
                }
            } catch (error) {
                console.error('Auth check failed:', error)
                storageService.clearToken()
            }
        }

        this.isAuthenticated = false
        this.user = null
        this.currentRoute = 'login'
    },
    
    initializeRouter() {
        router.init((route) => {
            this.currentRoute = route
        })
    },
    
    async login(credentials) {
        try {
            const response = await authService.login(credentials)
            if (response.success) {
                this.isAuthenticated = true
                this.user = response.user
                this.currentRoute = 'dashboard'
                return { success: true }
            }
            return { success: false, message: response.message }
        } catch (error) {
            console.error('Login error:', error)
            return { success: false, message: 'Login failed. Please try again.' }
        }
    },
    
    logout() {
        authService.logout()
        this.isAuthenticated = false
        this.user = null
        this.currentRoute = 'login'
        router.navigate('login')
    },
    
    // Navigation methods
    navigateTo(route) {
        router.navigate(route)
    },
    
    // Utility methods
    formatDate(date) {
        if (!date) return ''
        return new Date(date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        })
    },
    
    showToast(message, type = 'info') {
        // Simple toast implementation
        const toast = document.createElement('div')
        toast.className = `toast toast-top toast-center z-50`
        toast.innerHTML = `
            <div class="alert alert-${type}">
                <span>${message}</span>
            </div>
        `
        document.body.appendChild(toast)
        
        setTimeout(() => {
            toast.remove()
        }, 3000)
    }
}))

// Start Alpine.js
Alpine.start()

// Initialize app when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, starting app initialization...')
})
