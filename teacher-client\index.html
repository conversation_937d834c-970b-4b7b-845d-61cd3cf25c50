<!DOCTYPE html>
<html lang="en" data-theme="teacher">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <meta name="theme-color" content="#3b82f6">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Teacher App">
    
    <title>Teacher Reading Tracker</title>
    
    <!-- Preconnect to improve performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Inter font for better readability -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- <PERSON><PERSON> manifest -->
    <link rel="manifest" href="/manifest.json">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    
    <!-- iOS specific meta tags -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    
    <!-- Prevent zoom on input focus (iOS) -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
</head>
<body class="bg-base-100 font-sans antialiased">
    <!-- App container -->
    <div id="app" x-data="app" x-init="init()" class="min-h-screen-safe">
        <!-- Loading spinner shown before Alpine.js initializes -->
        <div id="initial-loader" class="flex items-center justify-center min-h-screen">
            <div class="loading loading-spinner loading-lg text-primary"></div>
        </div>

        <!-- App Loading State -->
        <div x-show="isLoading" class="flex items-center justify-center min-h-screen bg-base-100">
            <div class="text-center">
                <div class="loading loading-spinner loading-lg text-primary mb-4"></div>
                <p class="text-base-content/70">Loading Teacher App...</p>
            </div>
        </div>

        <!-- Login Screen -->
        <div x-show="!isLoading && !isAuthenticated && currentRoute === 'login'">
            <div x-data="loginForm" class="min-h-screen flex items-center justify-center bg-base-200 px-4">
                <div class="card w-full max-w-md bg-base-100 shadow-xl">
                    <div class="card-body">
                        <!-- Header -->
                        <div class="text-center mb-6">
                            <h1 class="text-3xl font-bold text-primary">Teacher App</h1>
                            <p class="text-base-content/70 mt-2">Sign in to your account</p>
                        </div>

                        <!-- Error Alert -->
                        <div x-show="errors.general" x-transition class="alert alert-error mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span x-text="errors.general"></span>
                        </div>

                        <!-- Login Form -->
                        <form @submit.prevent="handleSubmit" class="space-y-4">
                            <!-- Email Field -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text font-medium">Email</span>
                                </label>
                                <input
                                    x-ref="emailInput"
                                    x-model="email"
                                    @input="clearError('email')"
                                    @keypress="handleKeyPress"
                                    type="email"
                                    placeholder="Enter your email"
                                    :class="getInputClass('email')"
                                    :disabled="isLoading"
                                    autocomplete="email"
                                    required
                                />
                                <label x-show="hasError('email')" class="label">
                                    <span class="label-text-alt text-error" x-text="getError('email')"></span>
                                </label>
                            </div>

                            <!-- Password Field -->
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text font-medium">Password</span>
                                </label>
                                <div class="relative">
                                    <input
                                        x-model="password"
                                        @input="clearError('password')"
                                        @keypress="handleKeyPress"
                                        :type="showPassword ? 'text' : 'password'"
                                        placeholder="Enter your password"
                                        :class="getInputClass('password')"
                                        :disabled="isLoading"
                                        autocomplete="current-password"
                                        required
                                    />
                                    <button
                                        type="button"
                                        @click="togglePasswordVisibility"
                                        class="absolute right-3 top-1/2 transform -translate-y-1/2 btn btn-ghost btn-sm btn-circle"
                                        :disabled="isLoading"
                                    >
                                        <svg x-show="!showPassword" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                        </svg>
                                        <svg x-show="showPassword" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                                        </svg>
                                    </button>
                                </div>
                                <label x-show="hasError('password')" class="label">
                                    <span class="label-text-alt text-error" x-text="getError('password')"></span>
                                </label>
                            </div>

                            <!-- Remember Me -->
                            <div class="form-control">
                                <label class="label cursor-pointer justify-start gap-3">
                                    <input
                                        x-model="rememberMe"
                                        type="checkbox"
                                        class="checkbox checkbox-primary checkbox-sm"
                                        :disabled="isLoading"
                                    />
                                    <span class="label-text">Remember my email</span>
                                </label>
                            </div>

                            <!-- Submit Button -->
                            <button
                                type="submit"
                                class="btn btn-primary w-full btn-touch"
                                :disabled="isLoading"
                                :class="{ 'loading': isLoading }"
                            >
                                <span x-show="!isLoading">Sign In</span>
                                <span x-show="isLoading">Signing In...</span>
                            </button>
                        </form>

                        <!-- Footer -->
                        <div class="text-center mt-6 text-sm text-base-content/60">
                            <p>Need help? Contact your administrator</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main App Layout -->
        <div x-show="!isLoading && isAuthenticated" x-data="appLayout" class="min-h-screen bg-base-200">
            <!-- Pull to refresh indicator -->
            <div x-ref="pullIndicator" class="pull-to-refresh">
                <div class="flex items-center gap-2">
                    <div class="loading loading-spinner loading-sm"></div>
                    <span class="text-sm">Pull to refresh</span>
                </div>
            </div>

            <!-- Header -->
            <header class="navbar bg-primary text-primary-content sticky top-0 z-40 safe-area-top">
                <div class="navbar-start">
                    <button
                        @click="toggleSidebar"
                        class="btn btn-ghost btn-circle btn-touch"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>

                <div class="navbar-center">
                    <h1 class="text-lg font-semibold" x-text="currentRoute === 'dashboard' ? 'Dashboard' : 'Teacher App'"></h1>
                </div>

                <div class="navbar-end">
                    <button
                        @click="triggerRefresh"
                        class="btn btn-ghost btn-circle btn-touch"
                        :class="{ 'loading': isRefreshing }"
                        :disabled="isRefreshing"
                    >
                        <svg x-show="!isRefreshing" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                    </button>
                </div>
            </header>

            <!-- Sidebar -->
            <div
                x-show="showSidebar"
                x-transition:enter="transition ease-out duration-300"
                x-transition:enter-start="opacity-0"
                x-transition:enter-end="opacity-100"
                x-transition:leave="transition ease-in duration-200"
                x-transition:leave-start="opacity-100"
                x-transition:leave-end="opacity-0"
                @click="closeSidebar"
                class="fixed inset-0 bg-black bg-opacity-50 z-50"
            >
                <div
                    @click.stop
                    x-transition:enter="transition ease-out duration-300"
                    x-transition:enter-start="-translate-x-full"
                    x-transition:enter-end="translate-x-0"
                    x-transition:leave="transition ease-in duration-200"
                    x-transition:leave-start="translate-x-0"
                    x-transition:leave-end="-translate-x-full"
                    class="w-80 max-w-[80vw] h-full bg-base-100 shadow-xl"
                >
                    <!-- Sidebar Header -->
                    <div class="p-4 border-b border-base-200">
                        <div class="flex items-center gap-3">
                            <div class="avatar placeholder">
                                <div class="bg-primary text-primary-content rounded-full w-12">
                                    <span class="text-lg font-semibold" x-text="getUserDisplayName().charAt(0)"></span>
                                </div>
                            </div>
                            <div>
                                <div class="font-semibold" x-text="getUserDisplayName()"></div>
                                <div class="text-sm text-base-content/70" x-text="getUserRole()"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar Menu -->
                    <ul class="menu p-4 space-y-2">
                        <li>
                            <a @click="navigateTo('dashboard')" class="btn-touch">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6a2 2 0 01-2 2H10a2 2 0 01-2-2V5z" />
                                </svg>
                                Dashboard
                            </a>
                        </li>
                        <li>
                            <a @click="navigateTo('profile')" class="btn-touch">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                Profile
                            </a>
                        </li>
                        <li>
                            <a @click="navigateTo('settings')" class="btn-touch">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                Settings
                            </a>
                        </li>
                    </ul>

                    <!-- Sidebar Footer -->
                    <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-base-200">
                        <button
                            @click="logout"
                            class="btn btn-outline btn-error w-full btn-touch"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                            </svg>
                            Sign Out
                        </button>

                        <div x-show="lastRefresh" class="text-xs text-center text-base-content/50 mt-2">
                            Last updated: <span x-text="getLastRefreshTime()"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <main
                x-ref="mainContent"
                class="container-mobile min-h-screen pb-20 scroll-smooth-mobile"
                @refresh-data.window="triggerRefresh"
            >
                <!-- Dashboard Route -->
                <div x-show="currentRoute === 'dashboard'" x-data="dashboard">
                    <div class="py-6 space-y-6">
                        <!-- Loading State -->
                        <div x-show="isLoading" class="space-y-4">
                            <div class="skeleton-mobile h-20 w-full"></div>
                            <div class="grid grid-cols-2 gap-4">
                                <div class="skeleton-mobile h-24"></div>
                                <div class="skeleton-mobile h-24"></div>
                                <div class="skeleton-mobile h-24"></div>
                                <div class="skeleton-mobile h-24"></div>
                            </div>
                            <div class="space-y-3">
                                <div class="skeleton-mobile h-32 w-full"></div>
                                <div class="skeleton-mobile h-32 w-full"></div>
                            </div>
                        </div>

                        <!-- Error State -->
                        <div x-show="error && !isLoading" class="alert alert-error">
                            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span x-text="error"></span>
                            <button @click="refreshData" class="btn btn-sm btn-outline">Retry</button>
                        </div>

                        <!-- Dashboard Content -->
                        <div x-show="!isLoading && !error" class="space-y-6">
                            <!-- Welcome Section -->
                            <div class="card-mobile p-6">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h2 class="text-2xl font-bold text-base-content" x-text="getGreeting() + ', ' + getUserName()"></h2>
                                        <p class="text-base-content/70 mt-1">
                                            Active Term: <span class="font-medium" x-text="getActiveTermName()"></span>
                                        </p>
                                    </div>
                                    <div class="avatar placeholder">
                                        <div class="bg-primary text-primary-content rounded-full w-16">
                                            <span class="text-2xl font-bold" x-text="getUserName().charAt(0)"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Stats Grid -->
                            <div class="grid grid-cols-2 gap-4">
                                <div class="card-mobile p-4 text-center">
                                    <div class="text-2xl font-bold text-primary" x-text="stats.totalPrograms"></div>
                                    <div class="text-sm text-base-content/70">Total Programs</div>
                                </div>
                                <div class="card-mobile p-4 text-center">
                                    <div class="text-2xl font-bold text-success" x-text="stats.activePrograms"></div>
                                    <div class="text-sm text-base-content/70">Active Programs</div>
                                </div>
                                <div class="card-mobile p-4 text-center">
                                    <div class="text-2xl font-bold text-info" x-text="stats.totalStudents"></div>
                                    <div class="text-sm text-base-content/70">Students</div>
                                </div>
                                <div class="card-mobile p-4 text-center">
                                    <div class="text-2xl font-bold text-warning" x-text="stats.totalBooks"></div>
                                    <div class="text-sm text-base-content/70">Books</div>
                                </div>
                            </div>

                            <!-- Active Programs Section -->
                            <div x-show="hasActivePrograms()">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="text-lg font-semibold">Active Programs</h3>
                                    <span class="badge badge-primary" x-text="getActivePrograms().length"></span>
                                </div>

                                <div class="space-y-3">
                                    <template x-for="program in getActivePrograms()" :key="program.id">
                                        <div x-data="programCard" x-init="setProgram(program)" class="card-mobile hover:shadow-md transition-shadow cursor-pointer" @click="viewProgram()">
                                            <div class="p-4">
                                                <!-- Header -->
                                                <div class="flex items-start justify-between mb-3">
                                                    <div class="flex-1 min-w-0">
                                                        <h4 class="font-semibold text-base-content truncate" x-text="program?.name || 'Unnamed Program'"></h4>
                                                        <p class="text-sm text-base-content/70 mt-1 line-clamp-2" x-text="program?.description || 'No description available'"></p>
                                                    </div>
                                                    <div class="ml-3 flex-shrink-0">
                                                        <span class="badge badge-sm" :class="getStatusBadge()" x-text="getStatusText()"></span>
                                                    </div>
                                                </div>

                                                <!-- Program Dates -->
                                                <div x-show="program?.start_date || program?.end_date" class="flex items-center gap-4 text-xs text-base-content/60 mb-3">
                                                    <div x-show="program?.start_date" class="flex items-center gap-1">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                        </svg>
                                                        <span x-text="'Start: ' + formatDate(program.start_date)"></span>
                                                    </div>
                                                    <div x-show="program?.end_date" class="flex items-center gap-1">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                        </svg>
                                                        <span x-text="'End: ' + formatDate(program.end_date)"></span>
                                                    </div>
                                                </div>

                                                <!-- Progress Bar -->
                                                <div x-show="hasBooks()" class="mb-3">
                                                    <div class="flex items-center justify-between text-xs text-base-content/70 mb-1">
                                                        <span>Reading Progress</span>
                                                        <span x-text="getProgressPercentage() + '%'"></span>
                                                    </div>
                                                    <div class="w-full bg-base-300 rounded-full h-2">
                                                        <div
                                                            class="bg-primary h-2 rounded-full transition-all duration-300"
                                                            :style="`width: ${getProgressPercentage()}%`"
                                                        ></div>
                                                    </div>
                                                </div>

                                                <!-- Stats Row -->
                                                <div class="flex items-center justify-between text-sm">
                                                    <div class="flex items-center gap-4">
                                                        <!-- Teams -->
                                                        <div x-show="hasTeams()" class="flex items-center gap-1 text-base-content/70">
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                                            </svg>
                                                            <span x-text="getTeamCountText()"></span>
                                                        </div>

                                                        <!-- Students -->
                                                        <div x-show="hasStudents()" class="flex items-center gap-1 text-base-content/70">
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                                            </svg>
                                                            <span x-text="getStudentCountText()"></span>
                                                        </div>

                                                        <!-- Books -->
                                                        <div x-show="hasBooks()" class="flex items-center gap-1 text-base-content/70">
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                                            </svg>
                                                            <span x-text="getBookCountText()"></span>
                                                        </div>
                                                    </div>

                                                    <!-- View Arrow -->
                                                    <div class="flex-shrink-0">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-base-content/40" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                                        </svg>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                            </div>

                            <!-- Empty State -->
                            <div x-show="!hasPrograms()" class="text-center py-12">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-24 w-24 mx-auto text-base-content/30 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                </svg>
                                <h3 class="text-xl font-semibold text-base-content/70 mb-2">No Programs Available</h3>
                                <p class="text-base-content/50">You don't have any programs assigned yet.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Program Detail Route -->
                <div x-show="currentRoute === 'program'" x-data="programDetail">
                    <!-- Program detail content will be rendered here -->
                </div>
            </main>

            <!-- Bottom Navigation -->
            <nav x-data="bottomNav" class="nav-mobile">
                <div class="flex justify-around items-center py-2">
                    <button
                        @click="navigateTo('dashboard')"
                        class="flex flex-col items-center gap-1 p-2 btn-touch rounded-lg transition-colors"
                        :class="isActive('dashboard') ? 'text-primary bg-primary/10' : 'text-base-content/60 hover:text-base-content'"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6a2 2 0 01-2 2H10a2 2 0 01-2-2V5z" />
                        </svg>
                        <span class="text-xs font-medium">Dashboard</span>
                    </button>

                    <button
                        @click="navigateTo('programs')"
                        class="flex flex-col items-center gap-1 p-2 btn-touch rounded-lg transition-colors"
                        :class="isActive('programs') ? 'text-primary bg-primary/10' : 'text-base-content/60 hover:text-base-content'"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                        <span class="text-xs font-medium">Programs</span>
                    </button>

                    <button
                        @click="navigateTo('students')"
                        class="flex flex-col items-center gap-1 p-2 btn-touch rounded-lg transition-colors"
                        :class="isActive('students') ? 'text-primary bg-primary/10' : 'text-base-content/60 hover:text-base-content'"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                        </svg>
                        <span class="text-xs font-medium">Students</span>
                    </button>

                    <button
                        @click="navigateTo('profile')"
                        class="flex flex-col items-center gap-1 p-2 btn-touch rounded-lg transition-colors"
                        :class="isActive('profile') ? 'text-primary bg-primary/10' : 'text-base-content/60 hover:text-base-content'"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        <span class="text-xs font-medium">Profile</span>
                    </button>
                </div>
            </nav>
        </div>
    </div>

    <!-- Scripts -->
    <script type="module" src="/src/main.js"></script>
</body>
</html>
