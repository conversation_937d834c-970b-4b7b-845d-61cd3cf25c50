<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;

class Role extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'description',
        'level',
        'created_by',
        'updated_by',
    ];

    /**
     * Role level constants.
     */
    const LEVEL_SYSTEM_ADMIN = 1;
    const LEVEL_SCHOOL_ADMIN = 2;
    const LEVEL_TEACHER = 3;
    const LEVEL_STUDENT = 4;

    /**
     * Get all role levels.
     */
    public static function getLevels(): array
    {
        return [
            self::LEVEL_SYSTEM_ADMIN => __('admin.system_admin'),
            self::LEVEL_SCHOOL_ADMIN => __('admin.school_admin'),
            self::LEVEL_TEACHER => __('admin.teacher'),
            self::LEVEL_STUDENT => __('admin.student'),
        ];
    }

    /**
     * Get role level name.
     */
    public function getLevelNameAttribute(): string
    {
        return self::getLevels()[$this->level] ?? 'Unknown';
    }

    /**
     * Get users with this role.
     */
    public function users(): Has<PERSON>any
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get term users with this role.
     */
    public function termUsers(): HasMany
    {
        return $this->hasMany(TermUser::class);
    }

    /**
     * Check if this role can manage another role.
     */
    public function canManage(Role $role): bool
    {
        return $this->level < $role->level;
    }

    /**
     * Get roles that this role can manage.
     */
    public function scopeManageableBy($query, Role $role)
    {
        return $query->where('level', '>', $role->level);
    }

    /**
     * Scope to get roles by level.
     */
    public function scopeByLevel($query, int $level)
    {
        return $query->where('level', $level);
    }

    /**
     * Scope to get admin roles (level <= 2).
     */
    public function scopeAdminRoles($query)
    {
        return $query->where('level', '<=', self::LEVEL_SCHOOL_ADMIN);
    }

    /**
     * Check if this is a system admin role.
     */
    public function isSystemAdmin(): bool
    {
        return $this->level === self::LEVEL_SYSTEM_ADMIN;
    }

    /**
     * Check if this is a school admin role.
     */
    public function isSchoolAdmin(): bool
    {
        return $this->level === self::LEVEL_SCHOOL_ADMIN;
    }

    /**
     * Check if this is a teacher role.
     */
    public function isTeacher(): bool
    {
        return $this->level === self::LEVEL_TEACHER;
    }

    /**
     * Check if this is a student role.
     */
    public function isStudent(): bool
    {
        return $this->level === self::LEVEL_STUDENT;
    }
}
