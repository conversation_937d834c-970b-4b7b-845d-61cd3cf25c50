<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Program;
use App\Models\ProgramClass;
use App\Models\SchoolClass;
use App\MoonShine\Resources\BaseResource;
use App\MoonShine\Resources\ProgramResource;
use App\MoonShine\Resources\SchoolClassResource;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Components\Layout\Box;

/**
 * @extends BaseResource<ProgramClass>
 */
class ProgramClassResource extends BaseResource
{
    protected string $model = ProgramClass::class;

    protected array $with = ['program', 'schoolClass', 'creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.program_classes');
    }



    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name)
                ->sortable(),
            BelongsTo::make(__('admin.school_classes'), 'schoolClass', 
                formatted: fn(SchoolClass $class) => $class->name)
                ->sortable(),
            Text::make(__('admin.organizations'), 'schoolClass.organization.name')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(__('admin.programs'), 'program', 
                    formatted: fn(Program $program) => $program->name,
                    resource: ProgramResource::class)
                    ->required()
                    ->placeholder(__('admin.select_program')),
                
                BelongsTo::make(__('admin.school_classes'), 'schoolClass', 
                    formatted: fn(SchoolClass $class) => $class->name . ' (' . $class->organization->name . ')',
                    resource: SchoolClassResource::class)
                    ->required()
                    ->placeholder(__('admin.select_class'))
                    ->asyncSearch('name'),
            ]),
            
            ...parent::getAuditFields(),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            ...parent::getCommonDetailFields(),
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name),
            BelongsTo::make(__('admin.school_classes'), 'schoolClass', 
                formatted: fn(SchoolClass $class) => $class->name),
            Text::make(__('admin.organizations'), 'schoolClass.organization.name'),
        ];
    }

    protected function filters(): iterable
    {
        return [
            BelongsTo::make(__('admin.programs'), 'program', 
                formatted: fn(Program $program) => $program->name,
                resource: ProgramResource::class),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'program_id' => ['required', 'exists:programs,id'],
            'school_class_id' => ['required', 'exists:school_classes,id'],
            ...parent::getCommonRules($item),
        ];
    }

    public function getRedirectAfterSave(): string
    {
        return $this->getIndexPageUrl();
    }

    protected function checkCreatePermission($user): bool
    {
        // System admins and school admins can associate classes with programs
        return $user->isSystemAdmin() ||
               $user->hasRoleLevel(\App\Models\Role::LEVEL_SCHOOL_ADMIN);
    }

    protected function checkUpdatePermission($user): bool
    {
        // System admins and school admins can update program-class associations
        return $user->isSystemAdmin() ||
               $user->hasRoleLevel(\App\Models\Role::LEVEL_SCHOOL_ADMIN);
    }

    protected function checkDeletePermission($user): bool
    {
        // System admins and school admins can remove class associations
        return $user->isSystemAdmin() ||
               $user->hasRoleLevel(\App\Models\Role::LEVEL_SCHOOL_ADMIN);
    }
}
