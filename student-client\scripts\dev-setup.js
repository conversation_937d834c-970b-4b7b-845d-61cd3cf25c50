#!/usr/bin/env node

/**
 * Development setup script for Reading Quest Student Client
 * Helps with initial setup and common development tasks
 */

const fs = require('fs')
const path = require('path')

console.log('🎮 Reading Quest Student Client - Development Setup')
console.log('===================================================')

// Check if .env exists
const envPath = path.join(__dirname, '..', '.env')
if (!fs.existsSync(envPath)) {
    console.log('📝 Creating .env file from .env.example...')
    const envExamplePath = path.join(__dirname, '..', '.env.example')
    if (fs.existsSync(envExamplePath)) {
        fs.copyFileSync(envExamplePath, envPath)
        console.log('✅ .env file created')
    } else {
        console.log('❌ .env.example not found')
    }
} else {
    console.log('✅ .env file already exists')
}

// Check if node_modules exists
const nodeModulesPath = path.join(__dirname, '..', 'node_modules')
if (!fs.existsSync(nodeModulesPath)) {
    console.log('📦 Please run "npm install" to install dependencies')
} else {
    console.log('✅ Dependencies installed')
}

// Create placeholder icons if they don't exist
const publicPath = path.join(__dirname, '..', 'public')
const icon192Path = path.join(publicPath, 'icon-192.png')
const icon512Path = path.join(publicPath, 'icon-512.png')

if (!fs.existsSync(icon192Path) || !fs.existsSync(icon512Path)) {
    console.log('🎨 Creating placeholder icons...')
    console.log('   Note: Replace these with actual game-style icons for production')
    
    // Create simple placeholder files (you would replace these with actual icons)
    if (!fs.existsSync(icon192Path)) {
        fs.writeFileSync(icon192Path, '# Placeholder for 192x192 Reading Quest icon')
        console.log('   📱 Created placeholder icon-192.png')
    }
    
    if (!fs.existsSync(icon512Path)) {
        fs.writeFileSync(icon512Path, '# Placeholder for 512x512 Reading Quest icon')
        console.log('   📱 Created placeholder icon-512.png')
    }
}

// Create assets directories for game resources
const assetsPath = path.join(publicPath, 'assets')
const soundsPath = path.join(assetsPath, 'sounds')
const imagesPath = path.join(assetsPath, 'images')
const gamePath = path.join(imagesPath, 'game')

[assetsPath, soundsPath, imagesPath, gamePath].forEach(dir => {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true })
        console.log(`📁 Created directory: ${path.relative(path.join(__dirname, '..'), dir)}`)
    }
})

// Create placeholder game assets info
const gameAssetsInfo = path.join(gamePath, 'README.md')
if (!fs.existsSync(gameAssetsInfo)) {
    const content = `# Game Assets

This directory is for game-style visual assets:

## Recommended Sources:
- **Kenney.nl** - Free game assets (UI packs, icons)
- **itch.io** - Game art and UI elements
- **Freepik** - Educational and game graphics
- **Flaticon** - Game-style icons

## Asset Categories:
- \`backgrounds/\` - Game backgrounds and patterns
- \`characters/\` - Student avatar and character images
- \`ui/\` - Game UI elements (buttons, panels, decorations)
- \`achievements/\` - Badge and trophy graphics
- \`books/\` - Book cover placeholders and reading-related graphics

## Guidelines:
- Use consistent art style (cartoon/game-like)
- Optimize for mobile (small file sizes)
- Include multiple resolutions for different screen densities
- Use bright, engaging colors that match the app theme
`
    
    fs.writeFileSync(gameAssetsInfo, content)
    console.log('📖 Created game assets guide')
}

console.log('')
console.log('🎯 Next Steps:')
console.log('1. Update .env with your Laravel backend URL')
console.log('2. Run "npm run dev" to start development server')
console.log('3. Add game-style icons and assets to public/assets/')
console.log('4. Test on mobile devices for best gaming experience')
console.log('5. Consider adding sound effects and animations')
console.log('')
console.log('🎮 Game Development Tips:')
console.log('- Use bright, engaging colors')
console.log('- Add satisfying animations and feedback')
console.log('- Test touch interactions thoroughly')
console.log('- Consider gamification elements (points, levels, achievements)')
console.log('')
console.log('📚 Documentation: See README.md for detailed setup instructions')
console.log('🐛 Issues: Check browser console for any errors')
console.log('')
console.log('Ready to create an amazing reading adventure! 🚀✨')
