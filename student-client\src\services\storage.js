class StorageService {
    constructor() {
        this.prefix = 'reading_quest_'
        this.tokenKey = `${this.prefix}token`
        this.userKey = `${this.prefix}user`
        this.settingsKey = `${this.prefix}settings`
        this.gameDataKey = `${this.prefix}game_data`
        this.achievementsKey = `${this.prefix}achievements`
        this.progressKey = `${this.prefix}progress`
    }
    
    // Token management
    setToken(token) {
        try {
            localStorage.setItem(this.tokenKey, token)
            console.log('🔑 Token stored successfully')
        } catch (error) {
            console.error('🚨 Failed to store token:', error)
        }
    }
    
    getToken() {
        try {
            return localStorage.getItem(this.tokenKey)
        } catch (error) {
            console.error('🚨 Failed to get token:', error)
            return null
        }
    }
    
    clearToken() {
        try {
            localStorage.removeItem(this.tokenKey)
            console.log('🧹 Token cleared')
        } catch (error) {
            console.error('🚨 Failed to clear token:', error)
        }
    }
    
    // User data management
    setUser(user) {
        try {
            localStorage.setItem(this.user<PERSON><PERSON>, JSON.stringify(user))
            console.log('👤 User data stored successfully')
        } catch (error) {
            console.error('🚨 Failed to store user data:', error)
        }
    }
    
    getUser() {
        try {
            const userData = localStorage.getItem(this.userKey)
            return userData ? JSON.parse(userData) : null
        } catch (error) {
            console.error('🚨 Failed to get user data:', error)
            return null
        }
    }
    
    clearUser() {
        try {
            localStorage.removeItem(this.userKey)
            console.log('🧹 User data cleared')
        } catch (error) {
            console.error('🚨 Failed to clear user data:', error)
        }
    }
    
    // Game data management
    setGameData(gameData) {
        try {
            const currentData = this.getGameData()
            const updatedData = { ...currentData, ...gameData }
            localStorage.setItem(this.gameDataKey, JSON.stringify(updatedData))
            console.log('🎮 Game data updated')
        } catch (error) {
            console.error('🚨 Failed to store game data:', error)
        }
    }
    
    getGameData() {
        try {
            const gameData = localStorage.getItem(this.gameDataKey)
            return gameData ? JSON.parse(gameData) : this.getDefaultGameData()
        } catch (error) {
            console.error('🚨 Failed to get game data:', error)
            return this.getDefaultGameData()
        }
    }
    
    getDefaultGameData() {
        return {
            points: 0,
            level: 1,
            experience: 0,
            streak: 0,
            booksRead: 0,
            totalReadingTime: 0,
            achievements: [],
            lastLoginDate: null,
            preferences: {
                theme: 'default',
                soundEnabled: true,
                animationsEnabled: true
            }
        }
    }
    
    clearGameData() {
        try {
            localStorage.removeItem(this.gameDataKey)
            console.log('🧹 Game data cleared')
        } catch (error) {
            console.error('🚨 Failed to clear game data:', error)
        }
    }
    
    // Achievement management
    setAchievements(achievements) {
        try {
            localStorage.setItem(this.achievementsKey, JSON.stringify(achievements))
            console.log('🏆 Achievements updated')
        } catch (error) {
            console.error('🚨 Failed to store achievements:', error)
        }
    }
    
    getAchievements() {
        try {
            const achievements = localStorage.getItem(this.achievementsKey)
            return achievements ? JSON.parse(achievements) : []
        } catch (error) {
            console.error('🚨 Failed to get achievements:', error)
            return []
        }
    }
    
    addAchievement(achievement) {
        try {
            const achievements = this.getAchievements()
            if (!achievements.find(a => a.id === achievement.id)) {
                achievements.push({
                    ...achievement,
                    unlockedAt: new Date().toISOString()
                })
                this.setAchievements(achievements)
                console.log('🎉 New achievement unlocked:', achievement.name)
                return true
            }
            return false
        } catch (error) {
            console.error('🚨 Failed to add achievement:', error)
            return false
        }
    }
    
    // Progress tracking
    setProgress(programId, progress) {
        try {
            const allProgress = this.getProgress()
            allProgress[programId] = {
                ...allProgress[programId],
                ...progress,
                lastUpdated: new Date().toISOString()
            }
            localStorage.setItem(this.progressKey, JSON.stringify(allProgress))
            console.log('📈 Progress updated for program:', programId)
        } catch (error) {
            console.error('🚨 Failed to store progress:', error)
        }
    }
    
    getProgress(programId = null) {
        try {
            const progress = localStorage.getItem(this.progressKey)
            const allProgress = progress ? JSON.parse(progress) : {}
            return programId ? (allProgress[programId] || {}) : allProgress
        } catch (error) {
            console.error('🚨 Failed to get progress:', error)
            return programId ? {} : {}
        }
    }
    
    // Settings management
    setSettings(settings) {
        try {
            const currentSettings = this.getSettings()
            const updatedSettings = { ...currentSettings, ...settings }
            localStorage.setItem(this.settingsKey, JSON.stringify(updatedSettings))
            console.log('⚙️ Settings updated')
        } catch (error) {
            console.error('🚨 Failed to store settings:', error)
        }
    }
    
    getSettings() {
        try {
            const settings = localStorage.getItem(this.settingsKey)
            return settings ? JSON.parse(settings) : this.getDefaultSettings()
        } catch (error) {
            console.error('🚨 Failed to get settings:', error)
            return this.getDefaultSettings()
        }
    }
    
    getDefaultSettings() {
        return {
            theme: 'student',
            language: 'en',
            soundEnabled: true,
            animationsEnabled: true,
            notificationsEnabled: true,
            autoSave: true,
            difficulty: 'normal'
        }
    }
    
    clearSettings() {
        try {
            localStorage.removeItem(this.settingsKey)
            console.log('🧹 Settings cleared')
        } catch (error) {
            console.error('🚨 Failed to clear settings:', error)
        }
    }
    
    // Generic storage methods
    set(key, value) {
        try {
            const fullKey = `${this.prefix}${key}`
            const serializedValue = typeof value === 'string' ? value : JSON.stringify(value)
            localStorage.setItem(fullKey, serializedValue)
        } catch (error) {
            console.error(`🚨 Failed to store ${key}:`, error)
        }
    }
    
    get(key, defaultValue = null) {
        try {
            const fullKey = `${this.prefix}${key}`
            const value = localStorage.getItem(fullKey)
            if (value === null) return defaultValue
            
            try {
                return JSON.parse(value)
            } catch {
                return value
            }
        } catch (error) {
            console.error(`🚨 Failed to get ${key}:`, error)
            return defaultValue
        }
    }
    
    remove(key) {
        try {
            const fullKey = `${this.prefix}${key}`
            localStorage.removeItem(fullKey)
        } catch (error) {
            console.error(`🚨 Failed to remove ${key}:`, error)
        }
    }
    
    // Clear all app data
    clearAll() {
        try {
            const keys = Object.keys(localStorage)
            keys.forEach(key => {
                if (key.startsWith(this.prefix)) {
                    localStorage.removeItem(key)
                }
            })
            console.log('🧹 All Reading Quest data cleared')
        } catch (error) {
            console.error('🚨 Failed to clear all data:', error)
        }
    }
    
    // Check if localStorage is available
    isAvailable() {
        try {
            const test = '__storage_test__'
            localStorage.setItem(test, test)
            localStorage.removeItem(test)
            return true
        } catch {
            return false
        }
    }
    
    // Get storage usage info
    getStorageInfo() {
        if (!this.isAvailable()) {
            return { available: false }
        }
        
        try {
            let totalSize = 0
            let appSize = 0
            
            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    const size = localStorage[key].length
                    totalSize += size
                    
                    if (key.startsWith(this.prefix)) {
                        appSize += size
                    }
                }
            }
            
            return {
                available: true,
                totalSize,
                appSize,
                totalSizeKB: Math.round(totalSize / 1024 * 100) / 100,
                appSizeKB: Math.round(appSize / 1024 * 100) / 100
            }
        } catch (error) {
            console.error('🚨 Failed to get storage info:', error)
            return { available: true, error: error.message }
        }
    }
}

export const storageService = new StorageService()
