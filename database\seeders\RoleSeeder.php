<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Role;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            [
                'name' => 'System Administrator',
                'description' => 'Full system access and management',
                'level' => Role::LEVEL_SYSTEM_ADMIN,
            ],
            [
                'name' => 'School Administrator',
                'description' => 'Manages a single school',
                'level' => Role::LEVEL_SCHOOL_ADMIN,
            ],
            [
                'name' => 'Teacher',
                'description' => 'Teaches classes and manages students',
                'level' => Role::LEVEL_TEACHER,
            ],
            [
                'name' => 'Student',
                'description' => 'Participates in reading activities',
                'level' => Role::LEVEL_STUDENT,
            ],
        ];

        foreach ($roles as $roleData) {
            Role::firstOrCreate(
                ['name' => $roleData['name'], 'level' => $roleData['level']],
                $roleData
            );
        }
    }
}
