<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Role;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\ID;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Select;
use MoonShine\UI\Fields\Number;
use MoonShine\Support\Attributes\Icon;


#[Icon('shield-check')]
class RoleResource extends BaseResource
{
    protected string $model = Role::class;

    protected string $column = 'name';

    protected array $with = ['creator', 'updater'];

    public function getTitle(): string
    {
        return __('admin.roles');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),
            
            Text::make(__('admin.name'), 'name')
                ->sortable(),
            
            Text::make(__('admin.description'), 'description')
                ->sortable(),
            
            Select::make(__('admin.level'), 'level')
                ->options(Role::getLevels())
                ->sortable(),
            
            Text::make(__('admin.users_count'), 'users_count')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Flex::make([
                    Text::make(__('admin.name'), 'name')
                        ->required()
                        ->placeholder(__('admin.enter_name')),
                    
                    Select::make(__('admin.level'), 'level')
                        ->options(Role::getLevels())
                        ->required()
                        ->placeholder(__('admin.select_level')),
                ]),
                
                Text::make(__('admin.description'), 'description')
                    ->placeholder(__('admin.enter_description')),
            ]),
            
            
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            Text::make(__('admin.name'), 'name'),
            Select::make(__('admin.level'), 'level')
                ->options(Role::getLevels()),
            Text::make(__('admin.description'), 'description'),
            Number::make(__('admin.users_count'), 'users_count'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string', 'max:255'],
            'level' => ['required', 'integer', 'min:1', 'max:4'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function resolveQuery(): object
    {
        return parent::resolveQuery()
            ->withCount('users');
    }

    protected function checkCreatePermission($user): bool
    {
        // Only system admins can create roles
        return $user->isSystemAdmin();
    }

    protected function checkUpdatePermission($user): bool
    {
        // Only system admins can update roles
        return $user->isSystemAdmin();
    }

    protected function checkDeletePermission($user): bool
    {
        // Only system admins can delete roles
        return $user->isSystemAdmin();
    }

    protected function getSearchFields(): array
    {
        return ['name', 'description'];
    }
}
